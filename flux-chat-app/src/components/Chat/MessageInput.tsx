'use client';

import React, { useState, useRef, KeyboardEvent } from 'react';
import { PaperAirplaneIcon, MicrophoneIcon, PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface MessageInputProps {
  onSendMessage: (message: string, image?: { url: string; file: File }) => void;
  isLoading: boolean;
  placeholder?: string;
}

export default function MessageInput({
  onSendMessage,
  isLoading,
  placeholder = "描述您想要生成的图像..."
}: MessageInputProps) {
  const [message, setMessage] = useState('');
  const [selectedImage, setSelectedImage] = useState<{ url: string; file: File } | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if ((message.trim() || selectedImage) && !isLoading) {
      onSendMessage(message.trim(), selectedImage || undefined);
      setMessage('');
      setSelectedImage(null);
      // 重置textarea高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessage(e.target.value);

    // 自动调整textarea高度
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setSelectedImage({ url, file });
    }
  };

  const handleImageRemove = () => {
    if (selectedImage) {
      URL.revokeObjectURL(selectedImage.url);
      setSelectedImage(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleImageButtonClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="border-t bg-white p-4">
      {/* 图片预览区域 */}
      {selectedImage && (
        <div className="mb-4 relative inline-block">
          <img
            src={selectedImage.url}
            alt="上传的图片"
            className="max-w-xs max-h-32 rounded-lg border border-gray-300"
          />
          <button
            type="button"
            onClick={handleImageRemove}
            className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        </div>
      )}

      <form onSubmit={handleSubmit} className="flex items-end space-x-3">
        {/* 图片上传按钮 */}
        <button
          type="button"
          onClick={handleImageButtonClick}
          className="flex-shrink-0 p-2 text-gray-600 hover:text-blue-600 transition-colors"
          title="上传图片"
        >
          <PhotoIcon className="w-5 h-5" />
        </button>

        {/* 隐藏的文件输入 */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />

        {/* 语音输入按钮 (暂时禁用) */}
        <button
          type="button"
          disabled
          className="flex-shrink-0 p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
          title="语音输入 (即将推出)"
        >
          <MicrophoneIcon className="w-5 h-5" />
        </button>

        {/* 文本输入区域 */}
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={isLoading}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            style={{ minHeight: '44px', maxHeight: '120px' }}
            rows={1}
          />
          
          {/* 字符计数 */}
          {message.length > 0 && (
            <div className="absolute bottom-1 right-2 text-xs text-gray-400">
              {message.length}/500
            </div>
          )}
        </div>

        {/* 发送按钮 */}
        <button
          type="submit"
          disabled={(!message.trim() && !selectedImage) || isLoading}
          className="flex-shrink-0 p-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          title="发送消息 (Enter)"
        >
          {isLoading ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <PaperAirplaneIcon className="w-5 h-5" />
          )}
        </button>
      </form>

      {/* 快捷提示 */}
      <div className="mt-2 flex flex-wrap gap-2">
        {QUICK_PROMPTS.map((prompt, index) => (
          <button
            key={index}
            onClick={() => setMessage(prompt)}
            disabled={isLoading}
            className="text-xs px-3 py-1 bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {prompt}
          </button>
        ))}
      </div>
    </div>
  );
}

// 快捷提示词
const QUICK_PROMPTS = [
  "一只可爱的小猫",
  "未来城市风景",
  "油画风格的花朵",
  "卡通人物设计",
  "抽象艺术作品"
];
