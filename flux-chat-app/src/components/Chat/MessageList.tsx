'use client';

import React from 'react';
import { Message } from '@/lib/types';
import { UserIcon, ComputerDesktopIcon } from '@heroicons/react/24/outline';

interface MessageListProps {
  messages: Message[];
}

export default function MessageList({ messages }: MessageListProps) {
  return (
    <div className="flex-1 overflow-y-auto p-4 space-y-4">
      {messages.length === 0 ? (
        <div className="text-center text-gray-500 mt-8">
          <ComputerDesktopIcon className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <p className="text-lg font-medium">欢迎使用AI图像生成助手</p>
          <p className="text-sm mt-2">请描述您想要生成的图像，我会帮您优化提示词</p>
        </div>
      ) : (
        messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))
      )}
    </div>
  );
}

function MessageBubble({ message }: { message: Message }) {
  const isUser = message.role === 'user';
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
        {/* Avatar */}
        <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-blue-500' : 'bg-gray-500'
          }`}>
            {isUser ? (
              <UserIcon className="w-5 h-5 text-white" />
            ) : (
              <ComputerDesktopIcon className="w-5 h-5 text-white" />
            )}
          </div>
        </div>
        
        {/* Message Content */}
        <div className={`rounded-lg px-4 py-2 ${
          isUser
            ? 'bg-blue-500 text-white'
            : 'bg-gray-100 text-gray-900'
        }`}>
          {/* 图片内容 */}
          {message.image && (
            <div className="mb-3">
              <img
                src={message.image.url}
                alt="用户上传的图片"
                className="max-w-full max-h-64 rounded-lg border border-gray-300"
              />
            </div>
          )}

          {/* 文本内容 */}
          {message.content && (
            <p className="text-sm whitespace-pre-wrap">{message.content}</p>
          )}
          
          {/* Metadata */}
          {message.metadata && (
            <div className="mt-2 pt-2 border-t border-opacity-20 border-current">
              {message.metadata.type && (
                <span className={`text-xs px-2 py-1 rounded ${
                  isUser ? 'bg-blue-600' : 'bg-gray-200'
                }`}>
                  {getTypeLabel(message.metadata.type)}
                </span>
              )}
              
              {message.metadata.extractedElements && message.metadata.extractedElements.length > 0 && (
                <div className="mt-1">
                  <p className="text-xs opacity-75">提取的元素:</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {message.metadata.extractedElements.map((element, index) => (
                      <span 
                        key={index}
                        className={`text-xs px-1 py-0.5 rounded ${
                          isUser ? 'bg-blue-600' : 'bg-gray-200'
                        }`}
                      >
                        {element}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {/* Timestamp */}
          <p className={`text-xs mt-1 opacity-75`}>
            {formatTime(message.timestamp)}
          </p>
        </div>
      </div>
    </div>
  );
}

function getTypeLabel(type: string): string {
  const labels: Record<string, string> = {
    'clarification': '需求澄清',
    'prompt_generation': '提示词生成',
    'optimization': '优化建议',
    'analysis': '需求分析'
  };
  return labels[type] || type;
}

function formatTime(timestamp: Date): string {
  return new Date(timestamp).toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}
