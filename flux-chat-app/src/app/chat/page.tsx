'use client';

import { useState, useRef } from 'react';
import { PhotoIcon, XMarkIcon } from '@heroicons/react/24/outline';
import ImageDisplay from '@/components/Chat/ImageDisplay';
import ImageGenerationPanel from '@/components/Chat/ImageGenerationPanel';

interface ChatMessage {
  id: string;
  role: string;
  content: string;
  image?: {
    url: string;
    file: File;
  };
  images?: any[];
  metadata?: any;
}

export default function ChatPage() {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [optimizedPrompt, setOptimizedPrompt] = useState<string>('');
  const [selectedImage, setSelectedImage] = useState<{ url: string; file: File } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const sendMessage = async () => {
    if (!message.trim() && !selectedImage) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: message,
      image: selectedImage || undefined
    };

    setMessages(prev => [...prev, userMessage]);
    setMessage('');
    setSelectedImage(null);
    setIsLoading(true);

    try {
      // 准备请求数据
      const requestData: any = {
        message: userMessage.content
      };

      // 如果有图片，转换为base64
      if (selectedImage) {
        const base64 = await fileToBase64(selectedImage.file);
        requestData.image = {
          data: base64,
          mimeType: selectedImage.file.type
        };
      }

      // 直接生成优化提示词
      const response = await fetch('/api/optimize-prompt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          requirement: userMessage.content,
          conversationHistory: [],
          useAI: true,
          image: requestData.image
        })
      });

      const data = await response.json();

      if (response.ok && data.optimization?.optimizedPrompt) {
        setOptimizedPrompt(data.optimization.optimizedPrompt);

        const assistantMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: `已为您生成优化的提示词：\n\n${data.optimization.optimizedPrompt}`
        };

        setMessages(prev => [...prev, assistantMessage]);
      } else {
        setMessages(prev => [...prev, {
          id: (Date.now() + 1).toString(),
          role: 'assistant',
          content: '抱歉，生成提示词时发生了错误。请稍后重试。'
        }]);
      }
    } catch (error) {
      setMessages(prev => [...prev, {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: '网络错误，请检查连接。'
      }]);
    } finally {
      setIsLoading(false);
    }
  };



  // 处理图像生成
  const handleImageGeneration = async (config: any, prompt: string) => {
    setIsGeneratingImage(true);

    try {
      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          ...config
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // 添加图像消息
        const imageMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: `已为您生成图像！使用了 ${data.metadata.model_used} 模型。`,
          images: data.images,
          metadata: data.metadata
        };

        setMessages(prev => [...prev, imageMessage]);
      } else {
        // 处理错误
        const errorMessage: ChatMessage = {
          id: Date.now().toString(),
          role: 'assistant',
          content: data.demo_mode
            ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。'
            : `图像生成失败: ${data.error || '未知错误'}`
        };

        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '图像生成请求失败，请检查网络连接。'
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // 处理图片上传
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      const url = URL.createObjectURL(file);
      setSelectedImage({ url, file });
    }
  };

  // 移除图片
  const handleImageRemove = () => {
    if (selectedImage) {
      URL.revokeObjectURL(selectedImage.url);
      setSelectedImage(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // 点击图片按钮
  const handleImageButtonClick = () => {
    fileInputRef.current?.click();
  };

  // 将文件转换为base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // 移除data:image/jpeg;base64,前缀，只保留base64数据
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* 头部 */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <h1 className="text-xl font-semibold text-gray-900">AI图像提示词生成器</h1>
        <p className="text-sm text-gray-600">基于DeepSeek，直接生成专业的图像提示词</p>
      </div>

      {/* 消息区域 */}
      <div className="flex-1 overflow-y-auto p-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 mt-8">
            <p className="text-lg font-medium">AI图像提示词生成器</p>
            <p className="text-sm mt-2">描述您的图像需求，我将直接为您生成专业的提示词</p>
          </div>
        ) : (
          <div className="space-y-4 max-w-4xl mx-auto">
            {messages.map((msg) => (
              <div key={msg.id} className={`flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}>
                <div className={`max-w-[80%] ${msg.role === 'user' ? '' : 'w-full'}`}>
                  <div className={`rounded-lg px-4 py-2 ${
                    msg.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-900 border border-gray-200'
                  }`}>
                    {/* 图片内容 */}
                    {msg.image && (
                      <div className="mb-3">
                        <img
                          src={msg.image.url}
                          alt="用户上传的图片"
                          className="max-w-full max-h-64 rounded-lg border border-gray-300"
                        />
                      </div>
                    )}

                    {/* 文本内容 */}
                    {msg.content && (
                      <p className="whitespace-pre-wrap">{msg.content}</p>
                    )}
                  </div>

                  {/* 显示生成的图像 */}
                  {msg.images && msg.images.length > 0 && (
                    <div className="mt-3">
                      <ImageDisplay
                        images={msg.images}
                        metadata={msg.metadata}
                        onRegenerate={(seed) => {
                          if (msg.metadata?.prompt_used) {
                            handleImageGeneration(
                              {
                                model: msg.metadata.parameters?.model || 'flux-schnell',
                                size: msg.metadata.parameters?.size || 'landscape',
                                num_images: 1,
                                seed
                              },
                              msg.metadata.prompt_used
                            );
                          }
                        }}
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-white border border-gray-200 rounded-lg px-4 py-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    <span className="text-gray-500">AI正在思考...</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="max-w-4xl mx-auto space-y-3">
          {/* 图片预览区域 */}
          {selectedImage && (
            <div className="flex justify-center">
              <div className="relative inline-block">
                <img
                  src={selectedImage.url}
                  alt="上传的图片"
                  className="max-w-xs max-h-32 rounded-lg border border-gray-300"
                />
                <button
                  type="button"
                  onClick={handleImageRemove}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
            </div>
          )}
          {/* 图像生成面板 */}
          {optimizedPrompt && (
            <div className="flex justify-center">
              <ImageGenerationPanel
                onGenerate={handleImageGeneration}
                isGenerating={isGeneratingImage}
                optimizedPrompt={optimizedPrompt}
              />
            </div>
          )}

          {/* 消息输入 */}
          <div className="flex space-x-3">
            {/* 图片上传按钮 */}
            <button
              type="button"
              onClick={handleImageButtonClick}
              className="flex-shrink-0 p-2 text-gray-600 hover:text-blue-600 transition-colors"
              title="上传图片"
            >
              <PhotoIcon className="w-5 h-5" />
            </button>

            {/* 隐藏的文件输入 */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />

            <input
              type="text"
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder="描述您的图像需求，我将生成专业提示词..."
              disabled={isLoading || isGeneratingImage}
              className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            />
            <button
              onClick={sendMessage}
              disabled={(!message.trim() && !selectedImage) || isLoading || isGeneratingImage}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {isLoading ? '发送中...' : '发送'}
            </button>
          </div>

          {/* 快捷提示 */}
          <div className="flex flex-wrap gap-2 justify-center">
            {['可爱的小猫', '未来城市', '油画风景', '卡通人物', '抽象艺术'].map((prompt) => (
              <button
                key={prompt}
                onClick={() => setMessage(prompt)}
                disabled={isLoading || isGeneratingImage}
                className="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {prompt}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
