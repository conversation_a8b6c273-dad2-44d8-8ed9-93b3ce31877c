import { NextRequest, NextResponse } from 'next/server';
import { analyzeRequirement } from '@/lib/openai';
import { extractImageRequirement, analyzeKeywords } from '@/lib/promptOptimizer';

export async function POST(request: NextRequest) {
  try {
    const { message, conversationHistory, image } = await request.json();

    if (!message && !image) {
      return NextResponse.json(
        { error: 'Message or image is required' },
        { status: 400 }
      );
    }

    // 分析用户输入中的关键词
    const keywords = analyzeKeywords(message);
    
    // 从对话历史中提取图像需求
    const extractedRequirement = extractImageRequirement([...conversationHistory, message]);
    
    // 使用AI分析需求并生成回复（支持图片）
    const response = await analyzeRequirement(message || '', conversationHistory, image);
    
    // 检查是否需要进一步澄清
    const needsClarification = checkIfNeedsClarification(extractedRequirement);
    
    return NextResponse.json({
      response,
      extractedRequirement,
      keywords,
      needsClarification,
      metadata: {
        type: needsClarification ? 'clarification' : 'analysis',
        extractedElements: Object.keys(extractedRequirement),
      }
    });

  } catch (error) {
    console.error('Chat API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// 检查是否需要进一步澄清
function checkIfNeedsClarification(requirement: any): boolean {
  const requiredFields = ['subject', 'style', 'mood'];
  const missingFields = requiredFields.filter(field => !requirement[field]);
  
  // 如果缺少超过一半的关键信息，需要澄清
  return missingFields.length > requiredFields.length / 2;
}
