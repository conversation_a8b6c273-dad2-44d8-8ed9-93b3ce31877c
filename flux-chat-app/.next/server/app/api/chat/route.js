/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/route";
exports.ids = ["app/api/chat/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/route.ts */ \"(rsc)/./src/app/api/chat/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/route\",\n        pathname: \"/api/chat\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/chat/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_chat_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/route.ts":
/*!***********************************!*\
  !*** ./src/app/api/chat/route.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_openai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/openai */ \"(rsc)/./src/lib/openai.ts\");\n/* harmony import */ var _lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/promptOptimizer */ \"(rsc)/./src/lib/promptOptimizer.ts\");\n\n\n\nasync function POST(request) {\n    try {\n        const { message, conversationHistory, image } = await request.json();\n        if (!message && !image) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Message or image is required'\n            }, {\n                status: 400\n            });\n        }\n        // 分析用户输入中的关键词\n        const keywords = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.analyzeKeywords)(message);\n        // 从对话历史中提取图像需求\n        const extractedRequirement = (0,_lib_promptOptimizer__WEBPACK_IMPORTED_MODULE_2__.extractImageRequirement)([\n            ...conversationHistory,\n            message\n        ]);\n        // 使用AI分析需求并生成回复（支持图片）\n        const response = await (0,_lib_openai__WEBPACK_IMPORTED_MODULE_1__.analyzeRequirement)(message || '', conversationHistory, image);\n        // 检查是否需要进一步澄清\n        const needsClarification = checkIfNeedsClarification(extractedRequirement);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            response,\n            extractedRequirement,\n            keywords,\n            needsClarification,\n            metadata: {\n                type: needsClarification ? 'clarification' : 'analysis',\n                extractedElements: Object.keys(extractedRequirement)\n            }\n        });\n    } catch (error) {\n        console.error('Chat API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// 检查是否需要进一步澄清\nfunction checkIfNeedsClarification(requirement) {\n    const requiredFields = [\n        'subject',\n        'style',\n        'mood'\n    ];\n    const missingFields = requiredFields.filter((field)=>!requirement[field]);\n    // 如果缺少超过一半的关键信息，需要澄清\n    return missingFields.length > requiredFields.length / 2;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/openai.ts":
/*!***************************!*\
  !*** ./src/lib/openai.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PROMPT_OPTIMIZATION_PROMPT: () => (/* binding */ PROMPT_OPTIMIZATION_PROMPT),\n/* harmony export */   REQUIREMENT_ANALYSIS_PROMPT: () => (/* binding */ REQUIREMENT_ANALYSIS_PROMPT),\n/* harmony export */   aiClient: () => (/* binding */ aiClient),\n/* harmony export */   analyzeRequirement: () => (/* binding */ analyzeRequirement),\n/* harmony export */   getModel: () => (/* binding */ getModel),\n/* harmony export */   getProvider: () => (/* binding */ getProvider),\n/* harmony export */   optimizePrompt: () => (/* binding */ optimizePrompt)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\n// 初始化AI客户端 - 支持OpenAI和DeepSeek\nconst aiClient = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n    apiKey: process.env.DEEPSEEK_API_KEY || process.env.OPENAI_API_KEY,\n    baseURL: process.env.DEEPSEEK_API_KEY ? 'https://api.deepseek.com' : undefined\n});\n// 获取当前使用的模型\nconst getModel = ()=>{\n    if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {\n        return 'deepseek-chat';\n    }\n    return 'gpt-4';\n};\n// 获取当前AI提供商\nconst getProvider = ()=>{\n    if (process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here') {\n        return 'deepseek';\n    }\n    return 'openai';\n};\n// 系统提示词 - 用于需求理解和澄清\nconst REQUIREMENT_ANALYSIS_PROMPT = `你是一个专业的AI图像生成助手，专门帮助用户将模糊的图像需求转换为清晰、具体的描述。\n\n你的核心任务：\n1. 深度理解用户的图像生成需求和意图\n2. 智能识别描述中缺失的关键视觉信息\n3. 通过自然对话逐步获取完整的创作细节\n4. 提取并整理关键的视觉元素\n\n重点关注的视觉要素：\n- 主题内容：主体对象、场景、人物等\n- 艺术风格：写实、卡通、油画、水彩、数字艺术等\n- 情绪氛围：温暖、冷酷、神秘、浪漫、史诗等\n- 色彩搭配：主色调、配色方案、色彩情感\n- 构图布局：视角、景别、对称性、焦点\n- 光影效果：光源、明暗、氛围光、特殊效果\n- 质量要求：分辨率、细节程度、专业水准\n- 特殊元素：背景、道具、特效、风格化处理\n\n对话原则：\n- 使用温和、专业且富有启发性的语气\n- 每次只询问1-2个最关键的问题\n- 根据用户回答智能调整后续问题\n- 避免机械化的问答，保持对话的自然流畅\n- 适时提供创意建议和专业指导`;\n// 系统提示词 - 用于提示词优化\nconst PROMPT_OPTIMIZATION_PROMPT = `你是一位顶级的AI图像生成提示词优化专家，拥有丰富的视觉艺术和AI绘画经验。你的任务是将用户的创意需求转换为高效、精准的图像生成提示词。\n\n核心优化策略：\n1. 精确性：使用具体、明确的描述性词汇，避免模糊表达\n2. 层次性：按视觉重要性和影响力排序关键词\n3. 专业性：融入艺术风格、技术参数和行业术语\n4. 完整性：确保涵盖主体、风格、构图、光影、色彩等要素\n5. 兼容性：优化后的提示词应适用于主流AI绘画模型\n\n标准提示词架构：\n[核心主体] + [详细描述] + [艺术风格] + [构图视角] + [光影效果] + [色彩方案] + [质量增强] + [技术参数]\n\n输出要求：\n请严格按照JSON格式返回优化结果：\n{\n  \"originalPrompt\": \"用户原始描述\",\n  \"optimizedPrompt\": \"专业优化后的完整提示词\",\n  \"improvements\": [\"具体改进点1\", \"具体改进点2\", \"...\"],\n  \"confidence\": 0.85,\n  \"suggestedParameters\": {\n    \"aspectRatio\": \"推荐宽高比\",\n    \"style\": \"推荐风格设置\",\n    \"quality\": \"质量等级\"\n  }\n}\n\n优化重点：\n- 主体描述要具体生动，包含关键特征\n- 风格描述要专业准确，符合艺术分类\n- 构图要素要明确，包含视角和布局\n- 光影描述要富有表现力和技术性\n- 色彩搭配要和谐且有视觉冲击力\n- 质量词汇要权威，提升生成效果`;\n// 分析用户需求并生成澄清问题（支持图片）\nasync function analyzeRequirement(userInput, conversationHistory, image) {\n    // 检查是否有有效的API密钥\n    const hasValidKey = process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here' || process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';\n    if (!hasValidKey) {\n        return generateDemoResponse(userInput, conversationHistory, image);\n    }\n    try {\n        const model = getModel();\n        const provider = getProvider();\n        console.log(`Using ${provider} with model: ${model}`);\n        // 构建消息数组\n        const messages = [\n            {\n                role: 'system',\n                content: REQUIREMENT_ANALYSIS_PROMPT\n            },\n            ...conversationHistory.map((msg, index)=>({\n                    role: index % 2 === 0 ? 'user' : 'assistant',\n                    content: msg\n                }))\n        ];\n        // 构建用户消息，支持图片\n        const userMessage = {\n            role: 'user'\n        };\n        if (image && provider === 'deepseek') {\n            // DeepSeek支持视觉功能\n            userMessage.content = [\n                {\n                    type: 'text',\n                    text: userInput || '请分析这张图片，并帮我生成图像编辑或重新生成的提示词。'\n                },\n                {\n                    type: 'image_url',\n                    image_url: {\n                        url: `data:${image.mimeType};base64,${image.data}`\n                    }\n                }\n            ];\n        } else if (image && provider === 'openai') {\n            // OpenAI GPT-4V支持视觉功能\n            userMessage.content = [\n                {\n                    type: 'text',\n                    text: userInput || '请分析这张图片，并帮我生成图像编辑或重新生成的提示词。'\n                },\n                {\n                    type: 'image_url',\n                    image_url: {\n                        url: `data:${image.mimeType};base64,${image.data}`\n                    }\n                }\n            ];\n        } else {\n            // 纯文本消息\n            userMessage.content = userInput;\n        }\n        messages.push(userMessage);\n        const response = await aiClient.chat.completions.create({\n            model: image && provider === 'openai' ? 'gpt-4-vision-preview' : model,\n            messages,\n            temperature: 0.7,\n            max_tokens: 800,\n            // DeepSeek特定参数\n            ...provider === 'deepseek' && {\n                top_p: 0.9,\n                frequency_penalty: 0.1,\n                presence_penalty: 0.1\n            }\n        });\n        return response.choices[0]?.message?.content || '';\n    } catch (error) {\n        console.error(`Error analyzing requirement with ${getProvider()}:`, error);\n        // 回退到演示模式\n        return generateDemoResponse(userInput, conversationHistory, image);\n    }\n}\n// 演示模式响应生成器（支持图片）\nfunction generateDemoResponse(userInput, conversationHistory, image) {\n    const input = userInput.toLowerCase();\n    // 如果有图片，优先处理图片相关响应\n    if (image) {\n        if (!userInput || userInput.trim() === '') {\n            return '我看到您上传了一张图片！这张图片很有趣。请告诉我您想要：\\n\\n• 基于这张图片生成类似风格的新图像？\\n• 对这张图片进行编辑或修改？\\n• 提取图片中的元素用于新的创作？\\n• 改变图片的风格或氛围？\\n\\n请描述您的具体需求，我会帮您优化提示词！';\n        } else {\n            return `我看到您上传了图片并提到\"${userInput}\"。基于图片内容和您的描述，我建议：\\n\\n• 保持图片的主要构图和元素\\n• 根据您的要求调整风格和细节\\n• 优化色彩和光影效果\\n• 增强整体视觉表现力\\n\\n您还希望在哪些方面进行调整或改进呢？`;\n        }\n    }\n    // 简单的关键词检测和响应\n    if (conversationHistory.length === 0) {\n        // 首次交互\n        if (input.includes('猫') || input.includes('cat')) {\n            return '听起来很有趣！您想要什么风格的小猫图像呢？比如：\\n\\n• 写实风格的摄影作品\\n• 可爱的卡通风格\\n• 油画艺术风格\\n• 水彩画风格\\n\\n另外，您希望小猫在什么环境中呢？';\n        } else if (input.includes('风景') || input.includes('landscape')) {\n            return '风景画很棒！请告诉我更多细节：\\n\\n• 您想要什么类型的风景？（山脉、海滩、森林、城市等）\\n• 希望是什么时间？（日出、日落、夜晚等）\\n• 偏好什么艺术风格？\\n• 想要什么样的色彩氛围？';\n        } else if (input.includes('人物') || input.includes('人')) {\n            return '人物画像需要考虑很多细节！请告诉我：\\n\\n• 是肖像特写还是全身像？\\n• 想要什么风格？（写实、卡通、艺术风格等）\\n• 人物的年龄和特征？\\n• 背景环境如何？\\n• 服装风格有什么要求？';\n        } else {\n            return '很好的创意！为了帮您生成最佳的提示词，我需要了解更多细节：\\n\\n• 主要的视觉元素是什么？\\n• 您偏好什么艺术风格？\\n• 希望营造什么样的氛围？\\n• 有特定的色彩偏好吗？\\n\\n请详细描述您的想法！';\n        }\n    } else {\n        // 后续交互\n        if (input.includes('写实') || input.includes('摄影')) {\n            return '写实风格很棒！我建议：\\n\\n• 使用专业摄影术语\\n• 强调细节和质感\\n• 考虑光照效果\\n\\n还有其他细节需要补充吗？比如构图方式或特定的光照效果？';\n        } else if (input.includes('卡通') || input.includes('可爱')) {\n            return '卡通风格会很有趣！建议：\\n\\n• 强调可爱和友好的特征\\n• 使用明亮的色彩\\n• 简化的造型设计\\n\\n您希望是什么样的卡通风格？日式动漫、迪士尼风格，还是其他？';\n        } else if (input.includes('颜色') || input.includes('色彩')) {\n            return '色彩选择很重要！基于您的描述，我建议：\\n\\n• 温暖的色调营造舒适感\\n• 对比色增加视觉冲击\\n• 柔和的色彩营造宁静氛围\\n\\n您有特定的颜色偏好吗？';\n        } else {\n            return '很好！基于我们的对话，我已经收集到足够的信息来优化您的提示词了。让我为您生成一个专业的图像生成提示词！';\n        }\n    }\n}\n// 优化提示词\nasync function optimizePrompt(requirement, conversationContext) {\n    // 检查是否有有效的API密钥\n    const hasValidKey = process.env.DEEPSEEK_API_KEY && process.env.DEEPSEEK_API_KEY !== 'your_deepseek_api_key_here' || process.env.OPENAI_API_KEY && process.env.OPENAI_API_KEY !== 'your_openai_api_key_here';\n    if (!hasValidKey) {\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n    try {\n        const model = getModel();\n        const provider = getProvider();\n        console.log(`Optimizing prompt using ${provider} with model: ${model}`);\n        const response = await aiClient.chat.completions.create({\n            model: model,\n            messages: [\n                {\n                    role: 'system',\n                    content: PROMPT_OPTIMIZATION_PROMPT\n                },\n                {\n                    role: 'user',\n                    content: `请基于以下对话内容和用户需求，生成专业优化的图像生成提示词：\n\n## 对话上下文：\n${conversationContext}\n\n## 用户最终需求：\n${requirement}\n\n## 输出要求：\n请严格按照JSON格式返回优化结果，确保JSON格式正确且完整。`\n                }\n            ],\n            temperature: 0.2,\n            max_tokens: 1200,\n            // DeepSeek特定参数\n            ...provider === 'deepseek' && {\n                top_p: 0.8,\n                frequency_penalty: 0.0,\n                presence_penalty: 0.0\n            }\n        });\n        const content = response.choices[0]?.message?.content || '';\n        try {\n            // 尝试提取JSON内容\n            const jsonMatch = content.match(/\\{[\\s\\S]*\\}/);\n            const jsonContent = jsonMatch ? jsonMatch[0] : content;\n            const parsed = JSON.parse(jsonContent);\n            // 验证必要字段\n            if (!parsed.optimizedPrompt) {\n                throw new Error('Missing optimizedPrompt field');\n            }\n            return {\n                originalPrompt: requirement,\n                optimizedPrompt: parsed.optimizedPrompt,\n                improvements: parsed.improvements || [\n                    'AI优化提示词结构和专业术语'\n                ],\n                confidence: parsed.confidence || 0.85,\n                suggestedParameters: parsed.suggestedParameters || {\n                    aspectRatio: '16:9',\n                    style: 'auto',\n                    quality: 'high'\n                }\n            };\n        } catch (parseError) {\n            console.warn('JSON parsing failed, using fallback:', parseError);\n            // 如果JSON解析失败，返回基本结构\n            return {\n                originalPrompt: requirement,\n                optimizedPrompt: content.replace(/```json|```/g, '').trim(),\n                improvements: [\n                    `使用${provider}优化提示词`,\n                    '增强专业术语和结构'\n                ],\n                confidence: 0.8,\n                suggestedParameters: {\n                    aspectRatio: '16:9',\n                    style: 'auto',\n                    quality: 'high'\n                }\n            };\n        }\n    } catch (error) {\n        console.error(`Error optimizing prompt with ${getProvider()}:`, error);\n        // 回退到演示模式\n        return generateDemoOptimization(requirement, conversationContext);\n    }\n}\n// 演示模式的提示词优化 - 模拟DeepSeek风格\nfunction generateDemoOptimization(requirement, conversationContext) {\n    const context = conversationContext.toLowerCase();\n    const req = requirement.toLowerCase();\n    let optimizedPrompt = requirement;\n    const improvements = [];\n    // 基于内容添加专业优化 - DeepSeek风格\n    if (req.includes('猫') || req.includes('cat')) {\n        optimizedPrompt = `adorable cat, expressive eyes, fluffy fur texture, sitting pose, natural lighting, warm color palette, cozy indoor environment, professional pet photography, high resolution, detailed whiskers, soft focus background, 8k quality, award-winning composition`;\n        improvements.push('DeepSeek优化：增强了宠物摄影专业术语', '细化了毛发质感和眼神描述', '优化了环境氛围和光照设置', '添加了构图和质量增强词汇');\n    } else if (req.includes('风景') || req.includes('landscape')) {\n        optimizedPrompt = `breathtaking landscape vista, dramatic sky formation, golden hour illumination, vibrant natural colors, panoramic composition, depth of field, atmospheric perspective, professional landscape photography, ultra-high definition, cinematic quality, nature's masterpiece`;\n        improvements.push('DeepSeek优化：采用了电影级风景摄影术语', '强化了天空和光线的戏剧性', '增加了景深和透视效果', '提升了整体视觉冲击力');\n    } else if (req.includes('人物') || req.includes('portrait')) {\n        optimizedPrompt = `professional portrait photography, detailed facial features, expressive eyes, natural skin texture, studio lighting setup, shallow depth of field, artistic composition, high-end retouching, commercial quality, fashion photography style`;\n        improvements.push('DeepSeek优化：专业人像摄影技术术语', '强调了面部细节和皮肤质感', '优化了灯光和景深设置', '提升了商业摄影品质');\n    } else if (req.includes('未来') || req.includes('科幻')) {\n        optimizedPrompt = `futuristic concept art, cyberpunk aesthetic, neon lighting effects, high-tech architecture, digital art style, sci-fi atmosphere, advanced technology, holographic elements, cinematic composition, ultra-modern design, 4k digital artwork`;\n        improvements.push('DeepSeek优化：科幻概念艺术专业术语', '增强了赛博朋克视觉元素', '优化了未来科技感表达', '提升了数字艺术品质');\n    } else {\n        // 通用DeepSeek风格优化\n        optimizedPrompt = `${requirement}, masterful composition, professional quality, intricate details, optimal lighting, harmonious color scheme, artistic excellence, high-definition clarity, visual impact, creative interpretation`;\n        improvements.push('DeepSeek优化：提升了整体艺术表达', '增强了专业术语和技术参数', '优化了视觉效果描述', '改善了创意解释能力');\n    }\n    // 基于对话上下文的深度优化\n    if (context.includes('写实') || context.includes('摄影')) {\n        optimizedPrompt += ', photorealistic rendering, hyperrealistic details, camera-like precision, authentic textures';\n        improvements.push('DeepSeek深度优化：增强写实主义表现力');\n    } else if (context.includes('卡通') || context.includes('动漫')) {\n        optimizedPrompt += ', stylized animation, vibrant cartoon aesthetics, character design excellence, animated art style';\n        improvements.push('DeepSeek深度优化：强化动画艺术风格');\n    } else if (context.includes('油画')) {\n        optimizedPrompt += ', traditional oil painting technique, classical art mastery, painterly brushstrokes, fine art quality';\n        improvements.push('DeepSeek深度优化：提升古典绘画技法表达');\n    } else if (context.includes('水彩')) {\n        optimizedPrompt += ', watercolor transparency, fluid brushwork, artistic spontaneity, delicate color blending';\n        improvements.push('DeepSeek深度优化：增强水彩画艺术特色');\n    }\n    return {\n        originalPrompt: requirement,\n        optimizedPrompt,\n        improvements,\n        confidence: 0.92,\n        suggestedParameters: {\n            aspectRatio: context.includes('人物') ? '3:4' : '16:9',\n            style: context.includes('写实') ? 'photorealistic' : 'artistic',\n            quality: 'ultra-high',\n            model: 'deepseek-optimized'\n        }\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/openai.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/promptOptimizer.ts":
/*!************************************!*\
  !*** ./src/lib/promptOptimizer.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   analyzeKeywords: () => (/* binding */ analyzeKeywords),\n/* harmony export */   extractImageRequirement: () => (/* binding */ extractImageRequirement),\n/* harmony export */   generateOptimizedPrompt: () => (/* binding */ generateOptimizedPrompt)\n/* harmony export */ });\n// 艺术风格映射\nconst STYLE_MAPPINGS = {\n    '写实': 'photorealistic, hyperrealistic, detailed',\n    '卡通': 'cartoon style, animated, stylized',\n    '油画': 'oil painting, classical art, painterly',\n    '水彩': 'watercolor, soft brushstrokes, flowing',\n    '素描': 'pencil sketch, charcoal drawing, monochrome',\n    '数字艺术': 'digital art, concept art, modern',\n    '像素艺术': 'pixel art, 8-bit, retro gaming style',\n    '抽象': 'abstract art, non-representational, artistic',\n    '极简': 'minimalist, clean, simple composition',\n    '赛博朋克': 'cyberpunk, neon lights, futuristic, dystopian'\n};\n// 情绪氛围映射\nconst MOOD_MAPPINGS = {\n    '温暖': 'warm, cozy, inviting atmosphere',\n    '冷酷': 'cold, stark, dramatic lighting',\n    '神秘': 'mysterious, enigmatic, atmospheric',\n    '浪漫': 'romantic, dreamy, soft lighting',\n    '史诗': 'epic, grand, cinematic',\n    '宁静': 'peaceful, serene, calm',\n    '活力': 'energetic, vibrant, dynamic',\n    '忧郁': 'melancholic, moody, contemplative',\n    '恐怖': 'horror, dark, ominous',\n    '欢乐': 'joyful, cheerful, bright'\n};\n// 质量增强词汇\nconst QUALITY_ENHANCERS = [\n    'highly detailed',\n    'sharp focus',\n    'professional photography',\n    '8k resolution',\n    'award winning',\n    'masterpiece',\n    'trending on artstation',\n    'perfect composition'\n];\n// 光照效果映射\nconst LIGHTING_MAPPINGS = {\n    '自然光': 'natural lighting, soft daylight',\n    '金色时光': 'golden hour, warm sunset lighting',\n    '蓝色时光': 'blue hour, twilight, cool tones',\n    '戏剧性': 'dramatic lighting, high contrast',\n    '柔和': 'soft lighting, diffused light',\n    '背光': 'backlighting, rim light, silhouette',\n    '霓虹': 'neon lighting, colorful glow',\n    '月光': 'moonlight, nocturnal, ethereal',\n    '工作室': 'studio lighting, professional setup',\n    '环境光': 'ambient lighting, atmospheric'\n};\n// 构图方式映射\nconst COMPOSITION_MAPPINGS = {\n    '特写': 'close-up, detailed portrait',\n    '全身': 'full body shot, complete figure',\n    '中景': 'medium shot, waist up',\n    '远景': 'wide shot, establishing shot',\n    '鸟瞰': 'aerial view, top-down perspective',\n    '低角度': 'low angle, dramatic perspective',\n    '对称': 'symmetrical composition, balanced',\n    '三分法': 'rule of thirds, dynamic composition',\n    '中心构图': 'centered composition, focal point',\n    '对角线': 'diagonal composition, dynamic lines'\n};\n// 从对话中提取图像需求\nfunction extractImageRequirement(conversationHistory) {\n    const fullText = conversationHistory.join(' ').toLowerCase();\n    const requirement = {};\n    // 提取风格\n    for (const [key, value] of Object.entries(STYLE_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase()) || fullText.includes(value.toLowerCase())) {\n            requirement.style = key;\n            break;\n        }\n    }\n    // 提取情绪\n    for (const [key, value] of Object.entries(MOOD_MAPPINGS)){\n        if (fullText.includes(key.toLowerCase())) {\n            requirement.mood = key;\n            break;\n        }\n    }\n    // 提取颜色\n    const colors = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    const foundColors = colors.filter((color)=>fullText.includes(color));\n    if (foundColors.length > 0) {\n        requirement.colors = foundColors;\n    }\n    return requirement;\n}\n// 生成优化的提示词\nfunction generateOptimizedPrompt(requirement, originalPrompt) {\n    const promptParts = [];\n    const improvements = [];\n    // 添加主体描述\n    if (requirement.subject) {\n        promptParts.push(requirement.subject);\n    }\n    // 添加风格描述\n    if (requirement.style && STYLE_MAPPINGS[requirement.style]) {\n        const styleDesc = STYLE_MAPPINGS[requirement.style];\n        promptParts.push(styleDesc);\n        improvements.push(`添加了${requirement.style}风格的专业描述`);\n    }\n    // 添加构图描述\n    if (requirement.composition && COMPOSITION_MAPPINGS[requirement.composition]) {\n        const compDesc = COMPOSITION_MAPPINGS[requirement.composition];\n        promptParts.push(compDesc);\n        improvements.push(`优化了构图描述`);\n    }\n    // 添加光照效果\n    if (requirement.lighting && LIGHTING_MAPPINGS[requirement.lighting]) {\n        const lightDesc = LIGHTING_MAPPINGS[requirement.lighting];\n        promptParts.push(lightDesc);\n        improvements.push(`增强了光照效果描述`);\n    }\n    // 添加情绪氛围\n    if (requirement.mood && MOOD_MAPPINGS[requirement.mood]) {\n        const moodDesc = MOOD_MAPPINGS[requirement.mood];\n        promptParts.push(moodDesc);\n        improvements.push(`添加了${requirement.mood}氛围描述`);\n    }\n    // 添加颜色方案\n    if (requirement.colors && requirement.colors.length > 0) {\n        const colorDesc = `${requirement.colors.join(', ')} color scheme`;\n        promptParts.push(colorDesc);\n        improvements.push(`指定了颜色方案`);\n    }\n    // 添加质量增强词汇\n    const qualityEnhancers = QUALITY_ENHANCERS.slice(0, 3).join(', ');\n    promptParts.push(qualityEnhancers);\n    improvements.push('添加了质量增强词汇');\n    const optimizedPrompt = promptParts.join(', ');\n    // 计算信心度\n    const confidence = Math.min(0.9, 0.5 + improvements.length * 0.1);\n    return {\n        originalPrompt,\n        optimizedPrompt,\n        improvements,\n        confidence,\n        suggestedParameters: {\n            aspectRatio: '16:9',\n            style: requirement.style || 'auto',\n            quality: 'high'\n        }\n    };\n}\n// 分析用户输入中的关键词\nfunction analyzeKeywords(input) {\n    const text = input.toLowerCase();\n    const subjects = [];\n    const styles = [];\n    const moods = [];\n    const colors = [];\n    // 检测风格关键词\n    Object.keys(STYLE_MAPPINGS).forEach((style)=>{\n        if (text.includes(style.toLowerCase())) {\n            styles.push(style);\n        }\n    });\n    // 检测情绪关键词\n    Object.keys(MOOD_MAPPINGS).forEach((mood)=>{\n        if (text.includes(mood.toLowerCase())) {\n            moods.push(mood);\n        }\n    });\n    // 检测颜色关键词\n    const colorKeywords = [\n        '红',\n        '蓝',\n        '绿',\n        '黄',\n        '紫',\n        '橙',\n        '粉',\n        '黑',\n        '白',\n        '灰'\n    ];\n    colorKeywords.forEach((color)=>{\n        if (text.includes(color)) {\n            colors.push(color);\n        }\n    });\n    return {\n        subjects,\n        styles,\n        moods,\n        colors\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/promptOptimizer.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Froute&page=%2Fapi%2Fchat%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();