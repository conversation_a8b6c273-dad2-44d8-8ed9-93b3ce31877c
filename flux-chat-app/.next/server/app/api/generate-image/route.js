/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/api/generate-image/route.ts\",\n    nextConfigOutput,\n    userland: _Users_liupeng_cursor_Flux_flux_chat_app_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/imageGeneration */ \"(rsc)/./src/lib/imageGeneration.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { prompt, model = 'flux-schnell', size = 'landscape', num_images = 1, style_preset, guidance_scale, num_inference_steps, seed, negative_prompt, batch_mode = false } = body;\n        // 验证必需参数\n        if (!prompt || typeof prompt !== 'string') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Prompt is required and must be a string'\n            }, {\n                status: 400\n            });\n        }\n        // 构建生成参数\n        let params = {\n            prompt,\n            model,\n            size,\n            num_images: Math.min(num_images, 4),\n            guidance_scale,\n            num_inference_steps,\n            seed,\n            negative_prompt\n        };\n        // 检查API密钥，如果没有则返回演示结果\n        const hasValidFalKey = process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here' || process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here';\n        if (!hasValidFalKey) {\n            // 演示模式 - 返回模拟的图像生成结果\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                demo_mode: true,\n                images: [\n                    {\n                        url: 'https://picsum.photos/1024/768?random=' + Date.now(),\n                        width: 1024,\n                        height: 768,\n                        content_type: 'image/jpeg'\n                    }\n                ],\n                metadata: {\n                    model_used: params.model,\n                    generation_time: 3000 + Math.random() * 2000,\n                    seed: Math.floor(Math.random() * 1000000),\n                    prompt_used: prompt,\n                    parameters: params,\n                    demo_notice: 'This is a demo image. Configure FAL_API_KEY for real image generation.'\n                }\n            });\n        }\n        // 应用风格预设\n        if (style_preset && style_preset in _lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS) {\n            params = (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.applyStylePreset)(params, style_preset);\n        }\n        console.log('Image generation request:', {\n            prompt: prompt.substring(0, 100) + '...',\n            model,\n            size,\n            num_images: params.num_images\n        });\n        // 生成图像\n        const result = batch_mode && num_images > 1 ? await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateMultipleImages)(params, num_images) : await (0,_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.generateImage)(params);\n        if (result.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                images: result.images,\n                metadata: {\n                    model_used: result.model_used,\n                    generation_time: result.generation_time,\n                    seed: result.seed,\n                    prompt_used: prompt,\n                    parameters: params\n                }\n            });\n        } else {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: result.error,\n                success: false,\n                model_used: result.model_used\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error('Image generation API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET方法用于获取支持的模型和配置\nasync function GET() {\n    try {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            models: {\n                'flux-schnell': {\n                    name: 'FLUX Schnell',\n                    description: '快速生成，4步即可完成',\n                    speed: 'fast',\n                    quality: 'good'\n                },\n                'flux-dev': {\n                    name: 'FLUX Dev',\n                    description: '开发版本，平衡速度和质量',\n                    speed: 'medium',\n                    quality: 'high'\n                },\n                'flux-pro': {\n                    name: 'FLUX Pro',\n                    description: '专业版本，最高质量',\n                    speed: 'slow',\n                    quality: 'excellent'\n                },\n                'sd-xl': {\n                    name: 'Stable Diffusion XL',\n                    description: '经典的高质量模型',\n                    speed: 'medium',\n                    quality: 'high'\n                }\n            },\n            sizes: {\n                'square': '1024x1024',\n                'portrait': '768x1024',\n                'landscape': '1024x768',\n                'wide': '1344x768',\n                'tall': '768x1344'\n            },\n            style_presets: Object.keys(_lib_imageGeneration__WEBPACK_IMPORTED_MODULE_1__.STYLE_PRESETS),\n            limits: {\n                max_images_per_request: 4,\n                max_prompt_length: 1000\n            }\n        });\n    } catch (error) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to get configuration'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/imageGeneration.ts":
/*!************************************!*\
  !*** ./src/lib/imageGeneration.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IMAGE_MODELS: () => (/* binding */ IMAGE_MODELS),\n/* harmony export */   IMAGE_SIZES: () => (/* binding */ IMAGE_SIZES),\n/* harmony export */   IMAGE_SIZE_PIXELS: () => (/* binding */ IMAGE_SIZE_PIXELS),\n/* harmony export */   STYLE_PRESETS: () => (/* binding */ STYLE_PRESETS),\n/* harmony export */   applyStylePreset: () => (/* binding */ applyStylePreset),\n/* harmony export */   checkServiceStatus: () => (/* binding */ checkServiceStatus),\n/* harmony export */   generateImage: () => (/* binding */ generateImage),\n/* harmony export */   generateMultipleImages: () => (/* binding */ generateMultipleImages)\n/* harmony export */ });\n/* harmony import */ var _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @fal-ai/serverless-client */ \"(rsc)/./node_modules/@fal-ai/serverless-client/src/index.js\");\n\n// 配置fal.ai客户端\n_fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.config({\n    credentials: process.env.FAL_KEY || process.env.FAL_API_KEY\n});\n// 支持的图像生成模型\nconst IMAGE_MODELS = {\n    // FLUX模型 - 高质量图像生成\n    'flux-pro': 'fal-ai/flux-pro',\n    'flux-dev': 'fal-ai/flux/dev',\n    'flux-schnell': 'fal-ai/flux/schnell',\n    // Stable Diffusion模型\n    'sd-xl': 'fal-ai/stable-diffusion-xl',\n    'sd-3': 'fal-ai/stable-diffusion-v3-medium',\n    // 其他专业模型\n    'playground': 'fal-ai/playground-v2-5',\n    'kandinsky': 'fal-ai/kandinsky-3'\n};\n// 图像尺寸预设 - 映射到fal.ai的格式\nconst IMAGE_SIZES = {\n    'square': 'square_hd',\n    'portrait': 'portrait_4_3',\n    'landscape': 'landscape_4_3',\n    'wide': 'landscape_16_9',\n    'tall': 'portrait_16_9'\n};\n// 尺寸对应的实际像素值（用于显示）\nconst IMAGE_SIZE_PIXELS = {\n    'square_hd': {\n        width: 1024,\n        height: 1024\n    },\n    'square': {\n        width: 512,\n        height: 512\n    },\n    'portrait_4_3': {\n        width: 768,\n        height: 1024\n    },\n    'portrait_16_9': {\n        width: 576,\n        height: 1024\n    },\n    'landscape_4_3': {\n        width: 1024,\n        height: 768\n    },\n    'landscape_16_9': {\n        width: 1024,\n        height: 576\n    }\n};\n// 主要图像生成函数\nasync function generateImage(params) {\n    // 检查API密钥\n    const hasValidFalKey = process.env.FAL_KEY && process.env.FAL_KEY !== 'your_fal_api_key_here' || process.env.FAL_API_KEY && process.env.FAL_API_KEY !== 'your_fal_api_key_here';\n    if (!hasValidFalKey) {\n        return {\n            success: false,\n            error: 'FAL API key not configured',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n    try {\n        const model = params.model || 'flux-schnell';\n        const modelEndpoint = IMAGE_MODELS[model];\n        const imageSize = IMAGE_SIZES[params.size || 'landscape'];\n        console.log(`Generating image with model: ${model} (${modelEndpoint})`);\n        // 构建请求参数 - 根据不同模型使用不同的参数格式\n        let requestParams = {\n            prompt: params.prompt\n        };\n        // FLUX模型的参数格式\n        if (model.startsWith('flux')) {\n            requestParams = {\n                prompt: params.prompt,\n                image_size: imageSize,\n                num_images: params.num_images || 1,\n                num_inference_steps: getDefaultSteps(model),\n                enable_safety_checker: true,\n                ...params.seed && {\n                    seed: params.seed\n                }\n            };\n        } else {\n            // 其他模型的参数格式（SD等）\n            const pixelSize = IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'];\n            requestParams = {\n                prompt: params.prompt,\n                width: pixelSize.width,\n                height: pixelSize.height,\n                num_images: params.num_images || 1,\n                guidance_scale: params.guidance_scale || 7.5,\n                num_inference_steps: params.num_inference_steps || getDefaultSteps(model),\n                ...params.negative_prompt && {\n                    negative_prompt: params.negative_prompt\n                },\n                ...params.seed && {\n                    seed: params.seed\n                }\n            };\n        }\n        console.log(`Generating image with model: ${model} (${modelEndpoint})`);\n        console.log('Request parameters:', JSON.stringify(requestParams, null, 2));\n        const startTime = Date.now();\n        // 调用fal.ai API\n        const result = await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe(modelEndpoint, {\n            input: requestParams,\n            logs: true,\n            onQueueUpdate: (update)=>{\n                console.log('Queue update:', update);\n            }\n        });\n        const generationTime = Date.now() - startTime;\n        // fal.ai返回的数据直接在result中，不在result.data中\n        if (result && result.images && result.images.length > 0) {\n            const pixelSize = model.startsWith('flux') ? IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'] : IMAGE_SIZE_PIXELS[imageSize] || IMAGE_SIZE_PIXELS['landscape_4_3'];\n            return {\n                success: true,\n                images: result.images.map((img)=>({\n                        url: img.url,\n                        width: img.width || pixelSize.width,\n                        height: img.height || pixelSize.height,\n                        content_type: img.content_type || 'image/jpeg'\n                    })),\n                model_used: model,\n                generation_time: generationTime,\n                seed: result.seed\n            };\n        } else {\n            throw new Error('No images generated');\n        }\n    } catch (error) {\n        console.error('Image generation error:', error);\n        // 提取更详细的错误信息\n        let errorMessage = 'Unknown error occurred';\n        if (error instanceof Error) {\n            errorMessage = error.message;\n            // 如果是fal.ai的错误，尝试提取更多信息\n            if ('body' in error && error.body) {\n                console.error('Error body:', error.body);\n                errorMessage += ` (Status: ${error.status})`;\n            }\n        }\n        return {\n            success: false,\n            error: errorMessage,\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 获取模型默认步数\nfunction getDefaultSteps(model) {\n    const stepMap = {\n        'flux-pro': 50,\n        'flux-dev': 50,\n        'flux-schnell': 4,\n        'sd-xl': 30,\n        'sd-3': 28,\n        'playground': 50,\n        'kandinsky': 100\n    };\n    return stepMap[model] || 30;\n}\n// 批量图像生成\nasync function generateMultipleImages(params, count = 4) {\n    try {\n        const batchParams = {\n            ...params,\n            num_images: Math.min(count, 4)\n        };\n        return await generateImage(batchParams);\n    } catch (error) {\n        console.error('Batch image generation error:', error);\n        return {\n            success: false,\n            error: error instanceof Error ? error.message : 'Batch generation failed',\n            model_used: params.model || 'flux-schnell'\n        };\n    }\n}\n// 图像风格预设\nconst STYLE_PRESETS = {\n    'photorealistic': {\n        negative_prompt: 'cartoon, anime, painting, drawing, sketch, low quality, blurry',\n        guidance_scale: 7.5\n    },\n    'artistic': {\n        negative_prompt: 'low quality, blurry, distorted',\n        guidance_scale: 8.0\n    },\n    'anime': {\n        negative_prompt: 'realistic, photograph, low quality, blurry',\n        guidance_scale: 7.0\n    },\n    'cartoon': {\n        negative_prompt: 'realistic, photograph, dark, scary, low quality',\n        guidance_scale: 7.0\n    },\n    'cinematic': {\n        negative_prompt: 'low quality, amateur, snapshot, casual',\n        guidance_scale: 8.5\n    }\n};\n// 应用风格预设\nfunction applyStylePreset(params, style) {\n    const preset = STYLE_PRESETS[style];\n    return {\n        ...params,\n        negative_prompt: preset.negative_prompt,\n        guidance_scale: preset.guidance_scale\n    };\n}\n// 检查fal.ai服务状态\nasync function checkServiceStatus() {\n    try {\n        // 尝试调用一个简单的模型来检查服务状态\n        await _fal_ai_serverless_client__WEBPACK_IMPORTED_MODULE_0__.subscribe('fal-ai/flux/schnell', {\n            input: {\n                prompt: 'test',\n                image_size: 'square',\n                num_images: 1,\n                num_inference_steps: 1\n            },\n            timeout: 5000\n        });\n        return true;\n    } catch (error) {\n        console.error('Service status check failed:', error);\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/imageGeneration.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@msgpack","vendor-chunks/@fal-ai","vendor-chunks/eventsource-parser","vendor-chunks/robot3"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();