/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/page";
exports.ids = ["app/chat/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/page\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGc3JjJTJGYXBwJTJGY2hhdCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBMkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iXSwic291cmNlc0NvbnRlbnQiOlsiICBpbXBvcnQgeyBmaWxsTWV0YWRhdGFTZWdtZW50IH0gZnJvbSAnbmV4dC9kaXN0L2xpYi9tZXRhZGF0YS9nZXQtbWV0YWRhdGEtcm91dGUnXG5cbiAgZXhwb3J0IGRlZmF1bHQgYXN5bmMgKHByb3BzKSA9PiB7XG4gICAgY29uc3QgaW1hZ2VEYXRhID0ge1widHlwZVwiOlwiaW1hZ2UveC1pY29uXCIsXCJzaXplc1wiOlwiMTZ4MTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"719cb0fc3f63\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9zcmMvYXBwL2dsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNzE5Y2IwZmMzZjYzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_2___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_3___default().variable)} antialiased`,\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUlNQTtBQUtBQztBQVBpQjtBQVloQixNQUFNQyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0FBQ2YsRUFBRTtBQUVhLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLDJMQUFrQixDQUFDLENBQUMsRUFBRUMsZ01BQWtCLENBQUMsWUFBWSxDQUFDO3NCQUVuRUs7Ozs7Ozs7Ozs7O0FBSVQiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcblxuY29uc3QgZ2Vpc3RTYW5zID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtZ2Vpc3Qtc2Fuc1wiLFxuICBzdWJzZXRzOiBbXCJsYXRpblwiXSxcbn0pO1xuXG5jb25zdCBnZWlzdE1vbm8gPSBHZWlzdF9Nb25vKHtcbiAgdmFyaWFibGU6IFwiLS1mb250LWdlaXN0LW1vbm9cIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiQ3JlYXRlIE5leHQgQXBwXCIsXG4gIGRlc2NyaXB0aW9uOiBcIkdlbmVyYXRlZCBieSBjcmVhdGUgbmV4dCBhcHBcIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2dlaXN0U2Fucy52YXJpYWJsZX0gJHtnZWlzdE1vbm8udmFyaWFibGV9IGFudGlhbGlhc2VkYH1cbiAgICAgID5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJnZWlzdFNhbnMiLCJnZWlzdE1vbm8iLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsaXVwZW5nJTJGY3Vyc29yJTJGJUU4JUJGJTlCJUU5JTk4JUI2JTJGRmx1eCVFNSVBRiVCOSVFOCVBRiU5RCVFNSVCQyU4RiVFNSVCQSU5NCVFNyU5NCVBOCUyRmZsdXgtY2hhdC1hcHAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmxpdXBlbmclMkZjdXJzb3IlMkYlRTglQkYlOUIlRTklOTglQjYlMkZGbHV4JUU1JUFGJUI5JUU4JUFGJTlEJUU1JUJDJThGJUU1JUJBJTk0JUU3JTk0JUE4JTJGZmx1eC1jaGF0LWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmxpdXBlbmclMkZjdXJzb3IlMkYlRTglQkYlOUIlRTklOTglQjYlMkZGbHV4JUU1JUFGJUI5JUU4JUFGJTlEJUU1JUJDJThGJUU1JUJBJTk0JUU3JTk0JUE4JTJGZmx1eC1jaGF0LWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsaXVwZW5nJTJGY3Vyc29yJTJGJUU4JUJGJTlCJUU5JTk4JUI2JTJGRmx1eCVFNSVBRiVCOSVFOCVBRiU5RCVFNSVCQyU4RiVFNSVCQSU5NCVFNyU5NCVBOCUyRmZsdXgtY2hhdC1hcHAlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmxpdXBlbmclMkZjdXJzb3IlMkYlRTglQkYlOUIlRTklOTglQjYlMkZGbHV4JUU1JUFGJUI5JUU4JUFGJTlEJUU1JUJDJThGJUU1JUJBJTk0JUU3JTk0JUE4JTJGZmx1eC1jaGF0LWFwcCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUE2STtBQUM3STtBQUNBLDBPQUFnSjtBQUNoSjtBQUNBLDBPQUFnSjtBQUNoSjtBQUNBLG9SQUFxSztBQUNySztBQUNBLHdPQUErSTtBQUMvSTtBQUNBLDRQQUF5SjtBQUN6SjtBQUNBLGtRQUE0SjtBQUM1SjtBQUNBLHNRQUE4SiIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9odHRwLWFjY2Vzcy1mYWxsYmFjay9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2xpdXBlbmcvY3Vyc29yL+i/m+mYti9GbHV45a+56K+d5byP5bqU55SoL2ZsdXgtY2hhdC1hcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL21ldGFkYXRhLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvbGl1cGVuZy9jdXJzb3Iv6L+b6Zi2L0ZsdXjlr7nor53lvI/lupTnlKgvZmx1eC1jaGF0LWFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(ssr)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGbGl1cGVuZyUyRmN1cnNvciUyRiVFOCVCRiU5QiVFOSU5OCVCNiUyRkZsdXglRTUlQUYlQjklRTglQUYlOUQlRTUlQkMlOEYlRTUlQkElOTQlRTclOTQlQTglMkZmbHV4LWNoYXQtYXBwJTJGc3JjJTJGYXBwJTJGY2hhdCUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwSkFBMkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(ssr)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(ssr)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ChatPage() {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sendMessage = async ()=>{\n        if (!message.trim() && !selectedImage) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message,\n            image: selectedImage || undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setSelectedImage(null);\n        setIsLoading(true);\n        try {\n            // 准备请求数据\n            const requestData = {\n                message: userMessage.content\n            };\n            // 如果有图片，转换为base64\n            if (selectedImage) {\n                const base64 = await fileToBase64(selectedImage.file);\n                requestData.image = {\n                    data: base64,\n                    mimeType: selectedImage.file.type\n                };\n            }\n            // 直接生成优化提示词\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement: userMessage.content,\n                    conversationHistory: [],\n                    useAI: true,\n                    image: requestData.image\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.optimization?.optimizedPrompt) {\n                setOptimizedPrompt(data.optimization.optimizedPrompt);\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: `已为您生成优化的提示词：\\n\\n${data.optimization.optimizedPrompt}`\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，生成提示词时发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt, imageFile)=>{\n        setIsGeneratingImage(true);\n        try {\n            // 准备请求数据\n            const requestData = {\n                prompt,\n                ...config\n            };\n            // 如果有图片文件，转换为base64并添加到请求中\n            if (imageFile) {\n                try {\n                    const reader = new FileReader();\n                    const base64Promise = new Promise((resolve, reject)=>{\n                        reader.onload = ()=>resolve(reader.result);\n                        reader.onerror = reject;\n                        reader.readAsDataURL(imageFile);\n                    });\n                    const base64Data = await base64Promise;\n                    requestData.image_file = base64Data;\n                    console.log('Adding image to generation request');\n                } catch (error) {\n                    console.error('Failed to convert image to base64:', error);\n                }\n            }\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(requestData)\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const isImageEditing = data.metadata.model_used === 'flux-dev-img2img';\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: isImageEditing ? `已为您编辑图像！使用了 ${data.metadata.model_used} 模型进行图像编辑。` : `已为您生成图像！使用了 ${data.metadata.model_used} 模型。`,\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n                // 如果是图像编辑，清除上传的图片\n                if (isImageEditing && selectedImage) {\n                    handleImageRemove();\n                }\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : `图像生成失败: ${data.error || '未知错误'}`\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (e)=>{\n        const file = e.target.files?.[0];\n        if (file && file.type.startsWith('image/')) {\n            const url = URL.createObjectURL(file);\n            setSelectedImage({\n                url,\n                file\n            });\n        }\n    };\n    // 移除图片\n    const handleImageRemove = ()=>{\n        if (selectedImage) {\n            URL.revokeObjectURL(selectedImage.url);\n            setSelectedImage(null);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n    // 点击图片按钮\n    const handleImageButtonClick = ()=>{\n        fileInputRef.current?.click();\n    };\n    // 将文件转换为base64\n    const fileToBase64 = (file)=>{\n        return new Promise((resolve, reject)=>{\n            const reader = new FileReader();\n            reader.readAsDataURL(file);\n            reader.onload = ()=>{\n                const result = reader.result;\n                // 移除data:image/jpeg;base64,前缀，只保留base64数据\n                const base64 = result.split(',')[1];\n                resolve(base64);\n            };\n            reader.onerror = (error)=>reject(error);\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"AI图像提示词生成器\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"基于DeepSeek，直接生成专业的图像提示词\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"AI图像提示词生成器\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"描述您的图像需求，我将直接为您生成专业的提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `flex ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `max-w-[80%] ${msg.role === 'user' ? '' : 'w-full'}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `rounded-lg px-4 py-2 ${msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'}`,\n                                            children: [\n                                                msg.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: msg.image.url,\n                                                        alt: \"用户上传的图片\",\n                                                        className: \"max-w-full max-h-64 rounded-lg border border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 23\n                                                }, this),\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    if (msg.metadata?.prompt_used) {\n                                                        handleImageGeneration({\n                                                            model: msg.metadata.parameters?.model || 'flux-schnell',\n                                                            size: msg.metadata.parameters?.size || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedImage.url,\n                                        alt: \"上传的图片\",\n                                        className: \"max-w-xs max-h-32 rounded-lg border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleImageRemove,\n                                        className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: (config, prompt)=>handleImageGeneration(config, prompt, selectedImage?.file),\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleImageButtonClick,\n                                    className: \"flex-shrink-0 p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                    title: \"上传图片\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \"image/*\",\n                                    onChange: handleImageUpload,\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您的图像需求，我将生成专业提示词...\",\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && !selectedImage || isLoading || isGeneratingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 justify-center\",\n                            children: [\n                                '可爱的小猫',\n                                '未来城市',\n                                '油画风景',\n                                '卡通人物',\n                                '抽象艺术'\n                            ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setMessage(prompt),\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: prompt\n                                }, prompt, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 307,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 306,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 225,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageDisplay.tsx":
/*!**********************************************!*\
  !*** ./src/components/Chat/ImageDisplay.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(ssr)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ImageDisplay({ images, metadata, isLoading = false, onRegenerate, onVariation }) {\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedImages, setLikedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 下载图像\n    const downloadImage = async (imageUrl, index)=>{\n        try {\n            const response = await fetch(imageUrl);\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = `generated-image-${index + 1}.jpg`;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Download failed:', error);\n        }\n    };\n    // 分享图像\n    const shareImage = async (imageUrl)=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: 'AI生成的图像',\n                    text: metadata.prompt_used,\n                    url: imageUrl\n                });\n            } catch (error) {\n                console.error('Share failed:', error);\n            }\n        } else {\n            // 回退到复制链接\n            navigator.clipboard.writeText(imageUrl);\n            alert('图像链接已复制到剪贴板');\n        }\n    };\n    // 切换喜欢状态\n    const toggleLike = (index)=>{\n        const newLiked = new Set(likedImages);\n        if (newLiked.has(index)) {\n            newLiked.delete(index);\n        } else {\n            newLiked.add(index);\n        }\n        setLikedImages(newLiked);\n    };\n    // 格式化生成时间\n    const formatGenerationTime = (time)=>{\n        if (!time) return '未知';\n        if (time < 1000) return `${time}ms`;\n        return `${(time / 1000).toFixed(1)}s`;\n    };\n    // 获取模型显示名称\n    const getModelDisplayName = (model)=>{\n        const modelNames = {\n            'flux-schnell': 'FLUX Schnell',\n            'flux-dev': 'FLUX Dev',\n            'flux-pro': 'FLUX Pro',\n            'sd-xl': 'Stable Diffusion XL',\n            'sd-3': 'Stable Diffusion 3'\n        };\n        return modelNames[model] || model;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"正在生成图像...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"使用 \",\n                            getModelDisplayName(metadata.model_used),\n                            \" 模型生成中，请稍候\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    if (!images || images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"暂无图像\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"生成的图像将在这里显示\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `grid gap-4 ${images.length === 1 ? 'grid-cols-1' : images.length === 2 ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'}`,\n                    children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group cursor-pointer\",\n                            onClick: ()=>setSelectedImage(index),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: image.url,\n                                    alt: `Generated image ${index + 1}`,\n                                    className: \"w-full h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                    style: {\n                                        aspectRatio: `${image.width}/${image.height}`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setIsFullscreen(true);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"全屏查看\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    downloadImage(image.url, index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"下载图像\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    toggleLike(index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"喜欢\",\n                                                children: likedImages.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                selectedImage === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: [\n                    metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: [\n                                \"\\uD83C\\uDFAD \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"演示模式\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 18\n                                }, this),\n                                \": 这是示例图像。配置 FAL API 密钥以生成真实图像。\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/FAL_SETUP.md\",\n                                    className: \"text-yellow-900 underline ml-1\",\n                                    children: \"查看配置指南\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: getModelDisplayName(metadata.model_used)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-yellow-100 text-yellow-800 px-1 rounded\",\n                                                children: \"演示\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    metadata.generation_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatGenerationTime(metadata.generation_time)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    metadata.seed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Seed: \",\n                                            metadata.seed\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>shareImage(images[selectedImage].url),\n                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分享\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    onRegenerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onRegenerate(metadata.seed),\n                                        className: \"px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                        children: \"重新生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-gray-700\",\n                                children: \"提示词: \"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: metadata.prompt_used\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n                onClick: ()=>setIsFullscreen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-full max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: images[selectedImage].url,\n                            alt: \"Fullscreen view\",\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 text-white text-2xl hover:text-gray-300\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageDisplay.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Chat/ImageGenerationPanel.tsx":
/*!******************************************************!*\
  !*** ./src/components/Chat/ImageGenerationPanel.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageGenerationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ImageGenerationPanel({ onGenerate, isGenerating, optimizedPrompt }) {\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        model: 'flux-schnell',\n        size: 'landscape',\n        num_images: 1,\n        guidance_scale: 7.5\n    });\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载可用模型和配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            const loadConfig = {\n                \"ImageGenerationPanel.useEffect.loadConfig\": async ()=>{\n                    try {\n                        const response = await fetch('/api/generate-image');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setAvailableModels(data);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load image generation config:', error);\n                    }\n                }\n            }[\"ImageGenerationPanel.useEffect.loadConfig\"];\n            loadConfig();\n        }\n    }[\"ImageGenerationPanel.useEffect\"], []);\n    // 当有优化提示词时自动填充\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            if (optimizedPrompt && !customPrompt) {\n                setCustomPrompt(optimizedPrompt);\n            }\n        }\n    }[\"ImageGenerationPanel.useEffect\"], [\n        optimizedPrompt,\n        customPrompt\n    ]);\n    const handleGenerate = ()=>{\n        const promptToUse = customPrompt || optimizedPrompt || '';\n        if (!promptToUse.trim()) {\n            alert('请输入提示词或先优化对话中的提示词');\n            return;\n        }\n        onGenerate(config, promptToUse);\n        setIsOpen(false);\n    };\n    const getModelInfo = (modelKey)=>{\n        return availableModels.models?.[modelKey] || {\n            name: modelKey,\n            description: '未知模型',\n            speed: 'medium',\n            quality: 'good'\n        };\n    };\n    const getSpeedColor = (speed)=>{\n        switch(speed){\n            case 'fast':\n                return 'text-green-600 bg-green-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'slow':\n                return 'text-red-600 bg-red-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality){\n            case 'excellent':\n                return 'text-purple-600 bg-purple-50';\n            case 'high':\n                return 'text-blue-600 bg-blue-50';\n            case 'good':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onGenerate(config, optimizedPrompt),\n                    disabled: isGenerating,\n                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                    children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成图像\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(true),\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                    title: \"图像生成设置\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"设置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"图像生成设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-gray-500 hover:text-gray-700\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: customPrompt,\n                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                    placeholder: optimizedPrompt || \"输入图像描述...\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCustomPrompt(optimizedPrompt),\n                                    className: \"mt-2 text-sm text-blue-600 hover:text-blue-800\",\n                                    children: \"使用优化后的提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"生成模型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: Object.entries(availableModels.models || {}).map(([key, model])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    model: key\n                                                }),\n                                            className: `p-3 border rounded-lg cursor-pointer transition-colors ${config.model === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: model.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-2 py-1 text-xs rounded ${getSpeedColor(model.speed)}`,\n                                                                    children: model.speed\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `px-2 py-1 text-xs rounded ${getQualityColor(model.quality)}`,\n                                                                    children: model.quality\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: model.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"图像尺寸\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: Object.entries(availableModels.sizes || {}).map(([key, size])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    size: key\n                                                }),\n                                            className: `p-2 text-sm border rounded transition-colors ${config.size === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'}`,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: key\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: size\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"生成数量: \",\n                                        config.num_images\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"1\",\n                                    max: \"4\",\n                                    value: config.num_images,\n                                    onChange: (e)=>setConfig({\n                                            ...config,\n                                            num_images: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"1张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"4张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"border border-gray-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"p-3 cursor-pointer flex items-center space-x-2 hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"高级设置\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-gray-200 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"引导强度: \",\n                                                        config.guidance_scale\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"20\",\n                                                    step: \"0.5\",\n                                                    value: config.guidance_scale,\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            guidance_scale: parseFloat(e.target.value)\n                                                        }),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"创意\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"精确\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"推理步数 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"100\",\n                                                    value: config.num_inference_steps || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            num_inference_steps: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"使用默认值\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"随机种子 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.seed || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            seed: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"随机生成\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(false),\n                            className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerate,\n                            disabled: isGenerating || !customPrompt.trim(),\n                            className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"生成中...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"开始生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Chat/ImageGenerationPanel.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@heroicons","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();