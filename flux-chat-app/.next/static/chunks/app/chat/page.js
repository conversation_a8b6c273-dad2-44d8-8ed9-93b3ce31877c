/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/chat/page"],{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction AdjustmentsHorizontalIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75\"\n    }));\n}\n_c = AdjustmentsHorizontalIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(AdjustmentsHorizontalIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"AdjustmentsHorizontalIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowDownTrayIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3\"\n    }));\n}\n_c = ArrowDownTrayIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowDownTrayIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowDownTrayIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ClockIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z\"\n    }));\n}\n_c = ClockIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ClockIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ClockIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CogIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CogIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495\"\n    }));\n}\n_c = CogIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CogIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CogIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction CpuChipIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n    }));\n}\n_c = CpuChipIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CpuChipIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"CpuChipIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction HeartIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z\"\n    }));\n}\n_c = HeartIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(HeartIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"HeartIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction MagnifyingGlassIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z\"\n    }));\n}\n_c = MagnifyingGlassIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MagnifyingGlassIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MagnifyingGlassIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction PhotoIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = PhotoIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PhotoIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PhotoIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ShareIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M7.217 10.907a2.25 2.25 0 1 0 0 2.186m0-2.186c.18.324.283.696.283 1.093s-.103.77-.283 1.093m0-2.186 9.566-5.314m-9.566 7.5 9.566 5.314m0 0a2.25 2.25 0 1 0 3.935 2.186 2.25 2.25 0 0 0-3.935-2.186Zm0-12.814a2.25 2.25 0 1 0 3.933-2.185 2.25 2.25 0 0 0-3.933 2.185Z\"\n    }));\n}\n_c = ShareIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ShareIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ShareIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction SparklesIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z\"\n    }));\n}\n_c = SparklesIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(SparklesIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"SparklesIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction XMarkIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M6 18 18 6M6 6l12 12\"\n    }));\n}\n_c = XMarkIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(XMarkIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"XMarkIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction HeartIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        fill: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        d: \"m11.645 20.91-.007-.003-.022-.012a15.247 15.247 0 0 1-.383-.218 25.18 25.18 0 0 1-4.244-3.17C4.688 15.36 2.25 12.174 2.25 8.25 2.25 5.322 4.714 3 7.688 3A5.5 5.5 0 0 1 12 5.052 5.5 5.5 0 0 1 16.313 3c2.973 0 5.437 2.322 5.437 5.25 0 3.925-2.438 7.111-4.739 9.256a25.175 25.175 0 0 1-4.244 3.17 15.247 15.247 0 0 1-.383.219l-.022.012-.007.004-.003.001a.752.752 0 0 1-.704 0l-.003-.001Z\"\n    }));\n}\n_c = HeartIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(HeartIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"HeartIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(app-pages-browser)/./src/app/chat/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZsaXVwZW5nJTJGY3Vyc29yJTJGJUU4JUJGJTlCJUU5JTk4JUI2JTJGRmx1eCVFNSVBRiVCOSVFOCVBRiU5RCVFNSVCQyU4RiVFNSVCQSU5NCVFNyU5NCVBOCUyRmZsdXgtY2hhdC1hcHAlMkZzcmMlMkZhcHAlMkZjaGF0JTJGcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSx3S0FBMkciLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9hcHAvY2hhdC9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=PhotoIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const sendMessage = async ()=>{\n        if (!message.trim() && !selectedImage) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message,\n            image: selectedImage || undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        setSelectedImage(null);\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversationHistory: messages.map((m)=>m.content)\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                // 检查是否需要自动生成提示词优化\n                if (!data.needsClarification && data.extractedRequirement) {\n                    await generateOptimizedPrompt();\n                }\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 生成优化提示词\n    const generateOptimizedPrompt = async ()=>{\n        try {\n            const conversationHistory = messages.map((m)=>m.content);\n            const requirement = conversationHistory.join(' ');\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement,\n                    conversationHistory,\n                    useAI: true\n                })\n            });\n            if (response.ok) {\n                var _data_optimization;\n                const data = await response.json();\n                if ((_data_optimization = data.optimization) === null || _data_optimization === void 0 ? void 0 : _data_optimization.optimizedPrompt) {\n                    setOptimizedPrompt(data.optimization.optimizedPrompt);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to generate optimized prompt:', error);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt)=>{\n        setIsGeneratingImage(true);\n        try {\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    prompt,\n                    ...config\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: \"已为您生成图像！使用了 \".concat(data.metadata.model_used, \" 模型。\"),\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : \"图像生成失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file && file.type.startsWith('image/')) {\n            const url = URL.createObjectURL(file);\n            setSelectedImage({\n                url,\n                file\n            });\n        }\n    };\n    // 移除图片\n    const handleImageRemove = ()=>{\n        if (selectedImage) {\n            URL.revokeObjectURL(selectedImage.url);\n            setSelectedImage(null);\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = '';\n        }\n    };\n    // 点击图片按钮\n    const handleImageButtonClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"AI图像生成助手\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"让我帮您优化图像生成提示词\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 194,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"欢迎使用AI图像生成助手\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"请描述您想要生成的图像，我会帮您优化提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(msg.role === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] \".concat(msg.role === 'user' ? '' : 'w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg px-4 py-2 \".concat(msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'),\n                                            children: [\n                                                msg.image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-3\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                        src: msg.image.url,\n                                                        alt: \"用户上传的图片\",\n                                                        className: \"max-w-full max-h-64 rounded-lg border border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 23\n                                                }, this),\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    var _msg_metadata;\n                                                    if ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.prompt_used) {\n                                                        var _msg_metadata_parameters, _msg_metadata_parameters1;\n                                                        handleImageGeneration({\n                                                            model: ((_msg_metadata_parameters = msg.metadata.parameters) === null || _msg_metadata_parameters === void 0 ? void 0 : _msg_metadata_parameters.model) || 'flux-schnell',\n                                                            size: ((_msg_metadata_parameters1 = msg.metadata.parameters) === null || _msg_metadata_parameters1 === void 0 ? void 0 : _msg_metadata_parameters1.size) || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 15\n                            }, this)),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 207,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 200,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative inline-block\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: selectedImage.url,\n                                        alt: \"上传的图片\",\n                                        className: \"max-w-xs max-h-32 rounded-lg border border-gray-300\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        onClick: handleImageRemove,\n                                        className: \"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: handleImageGeneration,\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: handleImageButtonClick,\n                                    className: \"flex-shrink-0 p-2 text-gray-600 hover:text-blue-600 transition-colors\",\n                                    title: \"上传图片\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PhotoIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    ref: fileInputRef,\n                                    type: \"file\",\n                                    accept: \"image/*\",\n                                    onChange: handleImageUpload,\n                                    className: \"hidden\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您想要生成的图像...\",\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && !selectedImage || isLoading || isGeneratingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2 justify-center\",\n                            children: [\n                                '可爱的小猫',\n                                '未来城市',\n                                '油画风景',\n                                '卡通人物',\n                                '抽象艺术'\n                            ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setMessage(prompt),\n                                    disabled: isLoading || isGeneratingImage,\n                                    className: \"px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                    children: prompt\n                                }, prompt, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 273,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 192,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"+NPKr41MHxfUWE2KhgJBJpvqkNw=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx":
/*!**********************************************!*\
  !*** ./src/components/Chat/ImageDisplay.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageDisplay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownTrayIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/HeartIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownTrayIcon,ClockIcon,CpuChipIcon,HeartIcon,MagnifyingGlassIcon,PhotoIcon,ShareIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShareIcon.js\");\n/* harmony import */ var _barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=HeartIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/HeartIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction ImageDisplay(param) {\n    let { images, metadata, isLoading = false, onRegenerate, onVariation } = param;\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [likedImages, setLikedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // 下载图像\n    const downloadImage = async (imageUrl, index)=>{\n        try {\n            const response = await fetch(imageUrl);\n            const blob = await response.blob();\n            const url = window.URL.createObjectURL(blob);\n            const link = document.createElement('a');\n            link.href = url;\n            link.download = \"generated-image-\".concat(index + 1, \".jpg\");\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n            window.URL.revokeObjectURL(url);\n        } catch (error) {\n            console.error('Download failed:', error);\n        }\n    };\n    // 分享图像\n    const shareImage = async (imageUrl)=>{\n        if (navigator.share) {\n            try {\n                await navigator.share({\n                    title: 'AI生成的图像',\n                    text: metadata.prompt_used,\n                    url: imageUrl\n                });\n            } catch (error) {\n                console.error('Share failed:', error);\n            }\n        } else {\n            // 回退到复制链接\n            navigator.clipboard.writeText(imageUrl);\n            alert('图像链接已复制到剪贴板');\n        }\n    };\n    // 切换喜欢状态\n    const toggleLike = (index)=>{\n        const newLiked = new Set(likedImages);\n        if (newLiked.has(index)) {\n            newLiked.delete(index);\n        } else {\n            newLiked.add(index);\n        }\n        setLikedImages(newLiked);\n    };\n    // 格式化生成时间\n    const formatGenerationTime = (time)=>{\n        if (!time) return '未知';\n        if (time < 1000) return \"\".concat(time, \"ms\");\n        return \"\".concat((time / 1000).toFixed(1), \"s\");\n    };\n    // 获取模型显示名称\n    const getModelDisplayName = (model)=>{\n        const modelNames = {\n            'flux-schnell': 'FLUX Schnell',\n            'flux-dev': 'FLUX Dev',\n            'flux-pro': 'FLUX Pro',\n            'sd-xl': 'Stable Diffusion XL',\n            'sd-3': 'Stable Diffusion 3'\n        };\n        return modelNames[model] || model;\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"正在生成图像...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: [\n                            \"使用 \",\n                            getModelDisplayName(metadata.model_used),\n                            \" 模型生成中，请稍候\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 119,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, this);\n    }\n    if (!images || images.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-50 rounded-lg border border-gray-200 p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-gray-900 mb-2\",\n                        children: \"暂无图像\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"生成的图像将在这里显示\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg border border-gray-200 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-4 \".concat(images.length === 1 ? 'grid-cols-1' : images.length === 2 ? 'grid-cols-2' : 'grid-cols-2 md:grid-cols-3'),\n                    children: images.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group cursor-pointer\",\n                            onClick: ()=>setSelectedImage(index),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                    src: image.url,\n                                    alt: \"Generated image \".concat(index + 1),\n                                    className: \"w-full h-auto rounded-lg shadow-sm hover:shadow-md transition-shadow\",\n                                    style: {\n                                        aspectRatio: \"\".concat(image.width, \"/\").concat(image.height)\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    setIsFullscreen(true);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"全屏查看\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    downloadImage(image.url, index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"下载图像\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    toggleLike(index);\n                                                },\n                                                className: \"p-2 bg-white rounded-full shadow-lg hover:bg-gray-50 transition-colors\",\n                                                title: \"喜欢\",\n                                                children: likedImages.has(index) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_HeartIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4 text-red-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 23\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4 text-gray-700\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                selectedImage === index && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t border-gray-200 p-4 bg-gray-50\",\n                children: [\n                    metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-yellow-800\",\n                            children: [\n                                \"\\uD83C\\uDFAD \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"演示模式\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 18\n                                }, this),\n                                \": 这是示例图像。配置 FAL API 密钥以生成真实图像。\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/FAL_SETUP.md\",\n                                    className: \"text-yellow-900 underline ml-1\",\n                                    children: \"查看配置指南\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: getModelDisplayName(metadata.model_used)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            metadata.demo_notice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs bg-yellow-100 text-yellow-800 px-1 rounded\",\n                                                children: \"演示\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    metadata.generation_time && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: formatGenerationTime(metadata.generation_time)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    metadata.seed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs\",\n                                        children: [\n                                            \"Seed: \",\n                                            metadata.seed\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>shareImage(images[selectedImage].url),\n                                        className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownTrayIcon_ClockIcon_CpuChipIcon_HeartIcon_MagnifyingGlassIcon_PhotoIcon_ShareIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"分享\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    onRegenerate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onRegenerate(metadata.seed),\n                                        className: \"px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors\",\n                                        children: \"重新生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 227,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-gray-700\",\n                                children: \"提示词: \"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-600\",\n                                children: metadata.prompt_used\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                                lineNumber: 274,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this),\n            isFullscreen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\",\n                onClick: ()=>setIsFullscreen(false),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-full max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: images[selectedImage].url,\n                            alt: \"Fullscreen view\",\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsFullscreen(false),\n                            className: \"absolute top-4 right-4 text-white text-2xl hover:text-gray-300\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n                lineNumber: 280,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageDisplay.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageDisplay, \"dVJC2BGxr7IBjg45u7cntX42oJY=\");\n_c = ImageDisplay;\nvar _c;\n$RefreshReg$(_c, \"ImageDisplay\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NoYXQvSW1hZ2VEaXNwbGF5LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUV3QztBQVNIO0FBQ21DO0FBeUJ6RCxTQUFTVSxhQUFhLEtBTWpCO1FBTmlCLEVBQ25DQyxNQUFNLEVBQ05DLFFBQVEsRUFDUkMsWUFBWSxLQUFLLEVBQ2pCQyxZQUFZLEVBQ1pDLFdBQVcsRUFDTyxHQU5pQjs7SUFPbkMsTUFBTSxDQUFDQyxlQUFlQyxpQkFBaUIsR0FBR2hCLCtDQUFRQSxDQUFTO0lBQzNELE1BQU0sQ0FBQ2lCLGNBQWNDLGdCQUFnQixHQUFHbEIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDbUIsYUFBYUMsZUFBZSxHQUFHcEIsK0NBQVFBLENBQWMsSUFBSXFCO0lBRWhFLE9BQU87SUFDUCxNQUFNQyxnQkFBZ0IsT0FBT0MsVUFBa0JDO1FBQzdDLElBQUk7WUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU1IO1lBQzdCLE1BQU1JLE9BQU8sTUFBTUYsU0FBU0UsSUFBSTtZQUNoQyxNQUFNQyxNQUFNQyxPQUFPQyxHQUFHLENBQUNDLGVBQWUsQ0FBQ0o7WUFDdkMsTUFBTUssT0FBT0MsU0FBU0MsYUFBYSxDQUFDO1lBQ3BDRixLQUFLRyxJQUFJLEdBQUdQO1lBQ1pJLEtBQUtJLFFBQVEsR0FBRyxtQkFBNkIsT0FBVlosUUFBUSxHQUFFO1lBQzdDUyxTQUFTSSxJQUFJLENBQUNDLFdBQVcsQ0FBQ047WUFDMUJBLEtBQUtPLEtBQUs7WUFDVk4sU0FBU0ksSUFBSSxDQUFDRyxXQUFXLENBQUNSO1lBQzFCSCxPQUFPQyxHQUFHLENBQUNXLGVBQWUsQ0FBQ2I7UUFDN0IsRUFBRSxPQUFPYyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxvQkFBb0JBO1FBQ3BDO0lBQ0Y7SUFFQSxPQUFPO0lBQ1AsTUFBTUUsYUFBYSxPQUFPckI7UUFDeEIsSUFBSXNCLFVBQVVDLEtBQUssRUFBRTtZQUNuQixJQUFJO2dCQUNGLE1BQU1ELFVBQVVDLEtBQUssQ0FBQztvQkFDcEJDLE9BQU87b0JBQ1BDLE1BQU1yQyxTQUFTc0MsV0FBVztvQkFDMUJyQixLQUFLTDtnQkFDUDtZQUNGLEVBQUUsT0FBT21CLE9BQU87Z0JBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBQ2pDO1FBQ0YsT0FBTztZQUNMLFVBQVU7WUFDVkcsVUFBVUssU0FBUyxDQUFDQyxTQUFTLENBQUM1QjtZQUM5QjZCLE1BQU07UUFDUjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU1DLGFBQWEsQ0FBQzdCO1FBQ2xCLE1BQU04QixXQUFXLElBQUlqQyxJQUFJRjtRQUN6QixJQUFJbUMsU0FBU0MsR0FBRyxDQUFDL0IsUUFBUTtZQUN2QjhCLFNBQVNFLE1BQU0sQ0FBQ2hDO1FBQ2xCLE9BQU87WUFDTDhCLFNBQVNHLEdBQUcsQ0FBQ2pDO1FBQ2Y7UUFDQUosZUFBZWtDO0lBQ2pCO0lBRUEsVUFBVTtJQUNWLE1BQU1JLHVCQUF1QixDQUFDQztRQUM1QixJQUFJLENBQUNBLE1BQU0sT0FBTztRQUNsQixJQUFJQSxPQUFPLE1BQU0sT0FBTyxHQUFRLE9BQUxBLE1BQUs7UUFDaEMsT0FBTyxHQUE0QixPQUF6QixDQUFDQSxPQUFPLElBQUcsRUFBR0MsT0FBTyxDQUFDLElBQUc7SUFDckM7SUFFQSxXQUFXO0lBQ1gsTUFBTUMsc0JBQXNCLENBQUNDO1FBQzNCLE1BQU1DLGFBQXFDO1lBQ3pDLGdCQUFnQjtZQUNoQixZQUFZO1lBQ1osWUFBWTtZQUNaLFNBQVM7WUFDVCxRQUFRO1FBQ1Y7UUFDQSxPQUFPQSxVQUFVLENBQUNELE1BQU0sSUFBSUE7SUFDOUI7SUFFQSxJQUFJbEQsV0FBVztRQUNiLHFCQUNFLDhEQUFDb0Q7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNFO3dCQUFFRixXQUFVOzs0QkFBd0I7NEJBQy9CSixvQkFBb0JsRCxTQUFTeUQsVUFBVTs0QkFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBS3ZEO0lBRUEsSUFBSSxDQUFDMUQsVUFBVUEsT0FBTzJELE1BQU0sS0FBSyxHQUFHO1FBQ2xDLHFCQUNFLDhEQUFDTDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNoRSxtTEFBU0E7d0JBQUNnRSxXQUFVOzs7Ozs7a0NBQ3JCLDhEQUFDQzt3QkFBR0QsV0FBVTtrQ0FBeUM7Ozs7OztrQ0FDdkQsOERBQUNFO3dCQUFFRixXQUFVO2tDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJN0M7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVyxjQUlmLE9BSEN2RCxPQUFPMkQsTUFBTSxLQUFLLElBQUksZ0JBQ3RCM0QsT0FBTzJELE1BQU0sS0FBSyxJQUFJLGdCQUN0Qjs4QkFFQzNELE9BQU80RCxHQUFHLENBQUMsQ0FBQ0MsT0FBTy9DLHNCQUNsQiw4REFBQ3dDOzRCQUVDQyxXQUFVOzRCQUNWTyxTQUFTLElBQU14RCxpQkFBaUJROzs4Q0FFaEMsOERBQUNpRDtvQ0FDQ0MsS0FBS0gsTUFBTTNDLEdBQUc7b0NBQ2QrQyxLQUFLLG1CQUE2QixPQUFWbkQsUUFBUTtvQ0FDaEN5QyxXQUFVO29DQUNWVyxPQUFPO3dDQUFFQyxhQUFhLEdBQWtCTixPQUFmQSxNQUFNTyxLQUFLLEVBQUMsS0FBZ0IsT0FBYlAsTUFBTVEsTUFBTTtvQ0FBRzs7Ozs7OzhDQUl6RCw4REFBQ2Y7b0NBQUlDLFdBQVU7OENBQ2IsNEVBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ2U7Z0RBQ0NSLFNBQVMsQ0FBQ1M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCaEUsZ0JBQWdCO2dEQUNsQjtnREFDQStDLFdBQVU7Z0RBQ1ZsQixPQUFNOzBEQUVOLDRFQUFDNUMsbUxBQW1CQTtvREFBQzhELFdBQVU7Ozs7Ozs7Ozs7OzBEQUdqQyw4REFBQ2U7Z0RBQ0NSLFNBQVMsQ0FBQ1M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCNUQsY0FBY2lELE1BQU0zQyxHQUFHLEVBQUVKO2dEQUMzQjtnREFDQXlDLFdBQVU7Z0RBQ1ZsQixPQUFNOzBEQUVOLDRFQUFDN0MsbUxBQWlCQTtvREFBQytELFdBQVU7Ozs7Ozs7Ozs7OzBEQUcvQiw4REFBQ2U7Z0RBQ0NSLFNBQVMsQ0FBQ1M7b0RBQ1JBLEVBQUVDLGVBQWU7b0RBQ2pCN0IsV0FBVzdCO2dEQUNiO2dEQUNBeUMsV0FBVTtnREFDVmxCLE9BQU07MERBRUw1QixZQUFZb0MsR0FBRyxDQUFDL0IsdUJBQ2YsOERBQUNoQixpR0FBY0E7b0RBQUN5RCxXQUFVOzs7Ozt5RUFFMUIsOERBQUM1RCxtTEFBU0E7b0RBQUM0RCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQU81QmxELGtCQUFrQlMsdUJBQ2pCLDhEQUFDd0M7b0NBQUlDLFdBQVU7Ozs7Ozs7MkJBdkRaekM7Ozs7Ozs7Ozs7Ozs7OzswQkErRGIsOERBQUN3QztnQkFBSUMsV0FBVTs7b0JBRVp0RCxTQUFTd0UsV0FBVyxrQkFDbkIsOERBQUNuQjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ0U7NEJBQUVGLFdBQVU7O2dDQUEwQjs4Q0FDbEMsOERBQUNtQjs4Q0FBTzs7Ozs7O2dDQUFhOzhDQUN4Qiw4REFBQ0M7b0NBQUVsRCxNQUFLO29DQUFnQjhCLFdBQVU7OENBQWlDOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekUsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDt3Q0FBSUMsV0FBVTs7MERBQ2IsOERBQUMxRCxtTEFBV0E7Z0RBQUMwRCxXQUFVOzs7Ozs7MERBQ3ZCLDhEQUFDcUI7MERBQU16QixvQkFBb0JsRCxTQUFTeUQsVUFBVTs7Ozs7OzRDQUM3Q3pELFNBQVN3RSxXQUFXLGtCQUNuQiw4REFBQ0c7Z0RBQUtyQixXQUFVOzBEQUFxRDs7Ozs7Ozs7Ozs7O29DQUl4RXRELFNBQVM0RSxlQUFlLGtCQUN2Qiw4REFBQ3ZCO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQzNELG1MQUFTQTtnREFBQzJELFdBQVU7Ozs7OzswREFDckIsOERBQUNxQjswREFBTTVCLHFCQUFxQi9DLFNBQVM0RSxlQUFlOzs7Ozs7Ozs7Ozs7b0NBSXZENUUsU0FBUzZFLElBQUksa0JBQ1osOERBQUN4Qjt3Q0FBSUMsV0FBVTs7NENBQVU7NENBQ2hCdEQsU0FBUzZFLElBQUk7Ozs7Ozs7Ozs7Ozs7MENBSzFCLDhEQUFDeEI7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDZTt3Q0FDQ1IsU0FBUyxJQUFNNUIsV0FBV2xDLE1BQU0sQ0FBQ0ssY0FBYyxDQUFDYSxHQUFHO3dDQUNuRHFDLFdBQVU7OzBEQUVWLDhEQUFDN0QsbUxBQVNBO2dEQUFDNkQsV0FBVTs7Ozs7OzBEQUNyQiw4REFBQ3FCOzBEQUFLOzs7Ozs7Ozs7Ozs7b0NBR1B6RSw4QkFDQyw4REFBQ21FO3dDQUNDUixTQUFTLElBQU0zRCxhQUFhRixTQUFTNkUsSUFBSTt3Q0FDekN2QixXQUFVO2tEQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUVAsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ3FCO2dDQUFLckIsV0FBVTswQ0FBNEI7Ozs7OzswQ0FDNUMsOERBQUNxQjtnQ0FBS3JCLFdBQVU7MENBQWlCdEQsU0FBU3NDLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQUt4RGhDLDhCQUNDLDhEQUFDK0M7Z0JBQ0NDLFdBQVU7Z0JBQ1ZPLFNBQVMsSUFBTXRELGdCQUFnQjswQkFFL0IsNEVBQUM4QztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNROzRCQUNDQyxLQUFLaEUsTUFBTSxDQUFDSyxjQUFjLENBQUNhLEdBQUc7NEJBQzlCK0MsS0FBSTs0QkFDSlYsV0FBVTs7Ozs7O3NDQUVaLDhEQUFDZTs0QkFDQ1IsU0FBUyxJQUFNdEQsZ0JBQWdCOzRCQUMvQitDLFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWI7R0F2UXdCeEQ7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9jb21wb25lbnRzL0NoYXQvSW1hZ2VEaXNwbGF5LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFxuICBQaG90b0ljb24sXG4gIEFycm93RG93blRyYXlJY29uLFxuICBNYWduaWZ5aW5nR2xhc3NJY29uLFxuICBTaGFyZUljb24sXG4gIEhlYXJ0SWNvbixcbiAgQ2xvY2tJY29uLFxuICBDcHVDaGlwSWNvblxufSBmcm9tICdAaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUnO1xuaW1wb3J0IHsgSGVhcnRJY29uIGFzIEhlYXJ0U29saWRJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9zb2xpZCc7XG5cbmludGVyZmFjZSBHZW5lcmF0ZWRJbWFnZSB7XG4gIHVybDogc3RyaW5nO1xuICB3aWR0aDogbnVtYmVyO1xuICBoZWlnaHQ6IG51bWJlcjtcbiAgY29udGVudF90eXBlOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBJbWFnZU1ldGFkYXRhIHtcbiAgbW9kZWxfdXNlZDogc3RyaW5nO1xuICBnZW5lcmF0aW9uX3RpbWU/OiBudW1iZXI7XG4gIHNlZWQ/OiBudW1iZXI7XG4gIHByb21wdF91c2VkOiBzdHJpbmc7XG4gIHBhcmFtZXRlcnM6IGFueTtcbn1cblxuaW50ZXJmYWNlIEltYWdlRGlzcGxheVByb3BzIHtcbiAgaW1hZ2VzOiBHZW5lcmF0ZWRJbWFnZVtdO1xuICBtZXRhZGF0YTogSW1hZ2VNZXRhZGF0YTtcbiAgaXNMb2FkaW5nPzogYm9vbGVhbjtcbiAgb25SZWdlbmVyYXRlPzogKHNlZWQ/OiBudW1iZXIpID0+IHZvaWQ7XG4gIG9uVmFyaWF0aW9uPzogKGltYWdlVXJsOiBzdHJpbmcpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEltYWdlRGlzcGxheSh7IFxuICBpbWFnZXMsIFxuICBtZXRhZGF0YSwgXG4gIGlzTG9hZGluZyA9IGZhbHNlLFxuICBvblJlZ2VuZXJhdGUsXG4gIG9uVmFyaWF0aW9uIFxufTogSW1hZ2VEaXNwbGF5UHJvcHMpIHtcbiAgY29uc3QgW3NlbGVjdGVkSW1hZ2UsIHNldFNlbGVjdGVkSW1hZ2VdID0gdXNlU3RhdGU8bnVtYmVyPigwKTtcbiAgY29uc3QgW2lzRnVsbHNjcmVlbiwgc2V0SXNGdWxsc2NyZWVuXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2xpa2VkSW1hZ2VzLCBzZXRMaWtlZEltYWdlc10gPSB1c2VTdGF0ZTxTZXQ8bnVtYmVyPj4obmV3IFNldCgpKTtcblxuICAvLyDkuIvovb3lm77lg49cbiAgY29uc3QgZG93bmxvYWRJbWFnZSA9IGFzeW5jIChpbWFnZVVybDogc3RyaW5nLCBpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goaW1hZ2VVcmwpO1xuICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHJlc3BvbnNlLmJsb2IoKTtcbiAgICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICAgIGxpbmsuaHJlZiA9IHVybDtcbiAgICAgIGxpbmsuZG93bmxvYWQgPSBgZ2VuZXJhdGVkLWltYWdlLSR7aW5kZXggKyAxfS5qcGdgO1xuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICAgIGxpbmsuY2xpY2soKTtcbiAgICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XG4gICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEb3dubG9hZCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliIbkuqvlm77lg49cbiAgY29uc3Qgc2hhcmVJbWFnZSA9IGFzeW5jIChpbWFnZVVybDogc3RyaW5nKSA9PiB7XG4gICAgaWYgKG5hdmlnYXRvci5zaGFyZSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgYXdhaXQgbmF2aWdhdG9yLnNoYXJlKHtcbiAgICAgICAgICB0aXRsZTogJ0FJ55Sf5oiQ55qE5Zu+5YOPJyxcbiAgICAgICAgICB0ZXh0OiBtZXRhZGF0YS5wcm9tcHRfdXNlZCxcbiAgICAgICAgICB1cmw6IGltYWdlVXJsLFxuICAgICAgICB9KTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1NoYXJlIGZhaWxlZDonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIOWbnumAgOWIsOWkjeWItumTvuaOpVxuICAgICAgbmF2aWdhdG9yLmNsaXBib2FyZC53cml0ZVRleHQoaW1hZ2VVcmwpO1xuICAgICAgYWxlcnQoJ+WbvuWDj+mTvuaOpeW3suWkjeWItuWIsOWJqui0tOadvycpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliIfmjaLllpzmrKLnirbmgIFcbiAgY29uc3QgdG9nZ2xlTGlrZSA9IChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgY29uc3QgbmV3TGlrZWQgPSBuZXcgU2V0KGxpa2VkSW1hZ2VzKTtcbiAgICBpZiAobmV3TGlrZWQuaGFzKGluZGV4KSkge1xuICAgICAgbmV3TGlrZWQuZGVsZXRlKGluZGV4KTtcbiAgICB9IGVsc2Uge1xuICAgICAgbmV3TGlrZWQuYWRkKGluZGV4KTtcbiAgICB9XG4gICAgc2V0TGlrZWRJbWFnZXMobmV3TGlrZWQpO1xuICB9O1xuXG4gIC8vIOagvOW8j+WMlueUn+aIkOaXtumXtFxuICBjb25zdCBmb3JtYXRHZW5lcmF0aW9uVGltZSA9ICh0aW1lPzogbnVtYmVyKSA9PiB7XG4gICAgaWYgKCF0aW1lKSByZXR1cm4gJ+acquefpSc7XG4gICAgaWYgKHRpbWUgPCAxMDAwKSByZXR1cm4gYCR7dGltZX1tc2A7XG4gICAgcmV0dXJuIGAkeyh0aW1lIC8gMTAwMCkudG9GaXhlZCgxKX1zYDtcbiAgfTtcblxuICAvLyDojrflj5bmqKHlnovmmL7npLrlkI3np7BcbiAgY29uc3QgZ2V0TW9kZWxEaXNwbGF5TmFtZSA9IChtb2RlbDogc3RyaW5nKSA9PiB7XG4gICAgY29uc3QgbW9kZWxOYW1lczogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAgICdmbHV4LXNjaG5lbGwnOiAnRkxVWCBTY2huZWxsJyxcbiAgICAgICdmbHV4LWRldic6ICdGTFVYIERldicsXG4gICAgICAnZmx1eC1wcm8nOiAnRkxVWCBQcm8nLFxuICAgICAgJ3NkLXhsJzogJ1N0YWJsZSBEaWZmdXNpb24gWEwnLFxuICAgICAgJ3NkLTMnOiAnU3RhYmxlIERpZmZ1c2lvbiAzJyxcbiAgICB9O1xuICAgIHJldHVybiBtb2RlbE5hbWVzW21vZGVsXSB8fCBtb2RlbDtcbiAgfTtcblxuICBpZiAoaXNMb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHAtNlwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMTIgdy0xMiBib3JkZXItYi0yIGJvcmRlci1ibHVlLTUwMCBteC1hdXRvIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj7mraPlnKjnlJ/miJDlm77lg48uLi48L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPlxuICAgICAgICAgICAg5L2/55SoIHtnZXRNb2RlbERpc3BsYXlOYW1lKG1ldGFkYXRhLm1vZGVsX3VzZWQpfSDmqKHlnovnlJ/miJDkuK3vvIzor7fnqI3lgJlcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIGlmICghaW1hZ2VzIHx8IGltYWdlcy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTUwIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBwLTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxQaG90b0ljb24gY2xhc3NOYW1lPVwidy0xMiBoLTEyIHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIG1iLTJcIj7mmoLml6Dlm77lg488L2gzPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ncmF5LTYwMFwiPueUn+aIkOeahOWbvuWDj+WwhuWcqOi/memHjOaYvuekujwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgIHsvKiDlm77lg4/nvZHmoLwgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGdyaWQgZ2FwLTQgJHtcbiAgICAgICAgICBpbWFnZXMubGVuZ3RoID09PSAxID8gJ2dyaWQtY29scy0xJyA6IFxuICAgICAgICAgIGltYWdlcy5sZW5ndGggPT09IDIgPyAnZ3JpZC1jb2xzLTInIDogXG4gICAgICAgICAgJ2dyaWQtY29scy0yIG1kOmdyaWQtY29scy0zJ1xuICAgICAgICB9YH0+XG4gICAgICAgICAge2ltYWdlcy5tYXAoKGltYWdlLCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNlbGVjdGVkSW1hZ2UoaW5kZXgpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgc3JjPXtpbWFnZS51cmx9XG4gICAgICAgICAgICAgICAgYWx0PXtgR2VuZXJhdGVkIGltYWdlICR7aW5kZXggKyAxfWB9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtYXV0byByb3VuZGVkLWxnIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3dcIlxuICAgICAgICAgICAgICAgIHN0eWxlPXt7IGFzcGVjdFJhdGlvOiBgJHtpbWFnZS53aWR0aH0vJHtpbWFnZS5oZWlnaHR9YCB9fVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgey8qIOaCrOWBnOaTjeS9nCAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktMCBncm91cC1ob3ZlcjpiZy1vcGFjaXR5LTMwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCByb3VuZGVkLWxnIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG9wYWNpdHktMCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMFwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgICAgICAgICAgICAgICAgICAgIHNldElzRnVsbHNjcmVlbih0cnVlKTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBzaGFkb3ctbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5YWo5bGP5p+l55yLXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPE1hZ25pZnlpbmdHbGFzc0ljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICBkb3dubG9hZEltYWdlKGltYWdlLnVybCwgaW5kZXgpO1xuICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTIgYmctd2hpdGUgcm91bmRlZC1mdWxsIHNoYWRvdy1sZyBob3ZlcjpiZy1ncmF5LTUwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU9XCLkuIvovb3lm77lg49cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8QXJyb3dEb3duVHJheUljb24gY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWdyYXktNzAwXCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICB0b2dnbGVMaWtlKGluZGV4KTtcbiAgICAgICAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicC0yIGJnLXdoaXRlIHJvdW5kZWQtZnVsbCBzaGFkb3ctbGcgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlPVwi5Zac5qyiXCJcbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAge2xpa2VkSW1hZ2VzLmhhcyhpbmRleCkgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgPEhlYXJ0U29saWRJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1yZWQtNTAwXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICA8SGVhcnRJY29uIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ncmF5LTcwMFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICB7Lyog6YCJ5Lit5oyH56S65ZmoICovfVxuICAgICAgICAgICAgICB7c2VsZWN0ZWRJbWFnZSA9PT0gaW5kZXggJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBib3JkZXItMiBib3JkZXItYmx1ZS01MDAgcm91bmRlZC1sZyBwb2ludGVyLWV2ZW50cy1ub25lXCI+PC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWFg+aVsOaNruWSjOaTjeS9nCAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYm9yZGVyLXQgYm9yZGVyLWdyYXktMjAwIHAtNCBiZy1ncmF5LTUwXCI+XG4gICAgICAgIHsvKiDmvJTnpLrmqKHlvI/mj5DnpLogKi99XG4gICAgICAgIHttZXRhZGF0YS5kZW1vX25vdGljZSAmJiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0zIHAtMiBiZy15ZWxsb3ctNTAgYm9yZGVyIGJvcmRlci15ZWxsb3ctMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC15ZWxsb3ctODAwXCI+XG4gICAgICAgICAgICAgIPCfjq0gPHN0cm9uZz7mvJTnpLrmqKHlvI88L3N0cm9uZz46IOi/meaYr+ekuuS+i+WbvuWDj+OAgumFjee9riBGQUwgQVBJIOWvhumSpeS7peeUn+aIkOecn+WunuWbvuWDj+OAglxuICAgICAgICAgICAgICA8YSBocmVmPVwiL0ZBTF9TRVRVUC5tZFwiIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTkwMCB1bmRlcmxpbmUgbWwtMVwiPuafpeeci+mFjee9ruaMh+WNlzwvYT5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKX1cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtYi0zXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTQgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICA8Q3B1Q2hpcEljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgIDxzcGFuPntnZXRNb2RlbERpc3BsYXlOYW1lKG1ldGFkYXRhLm1vZGVsX3VzZWQpfTwvc3Bhbj5cbiAgICAgICAgICAgICAge21ldGFkYXRhLmRlbW9fbm90aWNlICYmIChcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXhzIGJnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwIHB4LTEgcm91bmRlZFwiPua8lOekujwvc3Bhbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7bWV0YWRhdGEuZ2VuZXJhdGlvbl90aW1lICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICA8Q2xvY2tJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICAgIDxzcGFuPntmb3JtYXRHZW5lcmF0aW9uVGltZShtZXRhZGF0YS5nZW5lcmF0aW9uX3RpbWUpfTwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7bWV0YWRhdGEuc2VlZCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAgICAgIFNlZWQ6IHttZXRhZGF0YS5zZWVkfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzaGFyZUltYWdlKGltYWdlc1tzZWxlY3RlZEltYWdlXS51cmwpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTEgdGV4dC1zbSBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIHJvdW5kZWQgaG92ZXI6YmctZ3JheS0yMDAgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0xXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPFNoYXJlSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+5YiG5LqrPC9zcGFuPlxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHtvblJlZ2VuZXJhdGUgJiYgKFxuICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb25SZWdlbmVyYXRlKG1ldGFkYXRhLnNlZWQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMSB0ZXh0LXNtIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICDph43mlrDnlJ/miJBcbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgXG4gICAgICAgIHsvKiDmj5DnpLror43mmL7npLogKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbVwiPlxuICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBcIj7mj5DnpLror406IDwvc3Bhbj5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwXCI+e21ldGFkYXRhLnByb21wdF91c2VkfTwvc3Bhbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIOWFqOWxj+aooeaAgeahhiAqL31cbiAgICAgIHtpc0Z1bGxzY3JlZW4gJiYgKFxuICAgICAgICA8ZGl2IFxuICAgICAgICAgIGNsYXNzTmFtZT1cImZpeGVkIGluc2V0LTAgYmctYmxhY2sgYmctb3BhY2l0eS05MCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHAtNFwiXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNGdWxsc2NyZWVuKGZhbHNlKX1cbiAgICAgICAgPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgbWF4LXctZnVsbCBtYXgtaC1mdWxsXCI+XG4gICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgIHNyYz17aW1hZ2VzW3NlbGVjdGVkSW1hZ2VdLnVybH1cbiAgICAgICAgICAgICAgYWx0PVwiRnVsbHNjcmVlbiB2aWV3XCJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibWF4LXctZnVsbCBtYXgtaC1mdWxsIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzRnVsbHNjcmVlbihmYWxzZSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC00IHJpZ2h0LTQgdGV4dC13aGl0ZSB0ZXh0LTJ4bCBob3Zlcjp0ZXh0LWdyYXktMzAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4pyVXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJQaG90b0ljb24iLCJBcnJvd0Rvd25UcmF5SWNvbiIsIk1hZ25pZnlpbmdHbGFzc0ljb24iLCJTaGFyZUljb24iLCJIZWFydEljb24iLCJDbG9ja0ljb24iLCJDcHVDaGlwSWNvbiIsIkhlYXJ0U29saWRJY29uIiwiSW1hZ2VEaXNwbGF5IiwiaW1hZ2VzIiwibWV0YWRhdGEiLCJpc0xvYWRpbmciLCJvblJlZ2VuZXJhdGUiLCJvblZhcmlhdGlvbiIsInNlbGVjdGVkSW1hZ2UiLCJzZXRTZWxlY3RlZEltYWdlIiwiaXNGdWxsc2NyZWVuIiwic2V0SXNGdWxsc2NyZWVuIiwibGlrZWRJbWFnZXMiLCJzZXRMaWtlZEltYWdlcyIsIlNldCIsImRvd25sb2FkSW1hZ2UiLCJpbWFnZVVybCIsImluZGV4IiwicmVzcG9uc2UiLCJmZXRjaCIsImJsb2IiLCJ1cmwiLCJ3aW5kb3ciLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiYm9keSIsImFwcGVuZENoaWxkIiwiY2xpY2siLCJyZW1vdmVDaGlsZCIsInJldm9rZU9iamVjdFVSTCIsImVycm9yIiwiY29uc29sZSIsInNoYXJlSW1hZ2UiLCJuYXZpZ2F0b3IiLCJzaGFyZSIsInRpdGxlIiwidGV4dCIsInByb21wdF91c2VkIiwiY2xpcGJvYXJkIiwid3JpdGVUZXh0IiwiYWxlcnQiLCJ0b2dnbGVMaWtlIiwibmV3TGlrZWQiLCJoYXMiLCJkZWxldGUiLCJhZGQiLCJmb3JtYXRHZW5lcmF0aW9uVGltZSIsInRpbWUiLCJ0b0ZpeGVkIiwiZ2V0TW9kZWxEaXNwbGF5TmFtZSIsIm1vZGVsIiwibW9kZWxOYW1lcyIsImRpdiIsImNsYXNzTmFtZSIsImgzIiwicCIsIm1vZGVsX3VzZWQiLCJsZW5ndGgiLCJtYXAiLCJpbWFnZSIsIm9uQ2xpY2siLCJpbWciLCJzcmMiLCJhbHQiLCJzdHlsZSIsImFzcGVjdFJhdGlvIiwid2lkdGgiLCJoZWlnaHQiLCJidXR0b24iLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiZGVtb19ub3RpY2UiLCJzdHJvbmciLCJhIiwic3BhbiIsImdlbmVyYXRpb25fdGltZSIsInNlZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx":
/*!******************************************************!*\
  !*** ./src/components/Chat/ImageGenerationPanel.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ImageGenerationPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,CogIcon,PhotoIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ImageGenerationPanel(param) {\n    let { onGenerate, isGenerating, optimizedPrompt } = param;\n    _s();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        model: 'flux-schnell',\n        size: 'landscape',\n        num_images: 1,\n        guidance_scale: 7.5\n    });\n    const [availableModels, setAvailableModels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [customPrompt, setCustomPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // 加载可用模型和配置\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            const loadConfig = {\n                \"ImageGenerationPanel.useEffect.loadConfig\": async ()=>{\n                    try {\n                        const response = await fetch('/api/generate-image');\n                        if (response.ok) {\n                            const data = await response.json();\n                            setAvailableModels(data);\n                        }\n                    } catch (error) {\n                        console.error('Failed to load image generation config:', error);\n                    }\n                }\n            }[\"ImageGenerationPanel.useEffect.loadConfig\"];\n            loadConfig();\n        }\n    }[\"ImageGenerationPanel.useEffect\"], []);\n    // 当有优化提示词时自动填充\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ImageGenerationPanel.useEffect\": ()=>{\n            if (optimizedPrompt && !customPrompt) {\n                setCustomPrompt(optimizedPrompt);\n            }\n        }\n    }[\"ImageGenerationPanel.useEffect\"], [\n        optimizedPrompt,\n        customPrompt\n    ]);\n    const handleGenerate = ()=>{\n        const promptToUse = customPrompt || optimizedPrompt || '';\n        if (!promptToUse.trim()) {\n            alert('请输入提示词或先优化对话中的提示词');\n            return;\n        }\n        onGenerate(config, promptToUse);\n        setIsOpen(false);\n    };\n    const getModelInfo = (modelKey)=>{\n        var _availableModels_models;\n        return ((_availableModels_models = availableModels.models) === null || _availableModels_models === void 0 ? void 0 : _availableModels_models[modelKey]) || {\n            name: modelKey,\n            description: '未知模型',\n            speed: 'medium',\n            quality: 'good'\n        };\n    };\n    const getSpeedColor = (speed)=>{\n        switch(speed){\n            case 'fast':\n                return 'text-green-600 bg-green-50';\n            case 'medium':\n                return 'text-yellow-600 bg-yellow-50';\n            case 'slow':\n                return 'text-red-600 bg-red-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    const getQualityColor = (quality)=>{\n        switch(quality){\n            case 'excellent':\n                return 'text-purple-600 bg-purple-50';\n            case 'high':\n                return 'text-blue-600 bg-blue-50';\n            case 'good':\n                return 'text-green-600 bg-green-50';\n            default:\n                return 'text-gray-600 bg-gray-50';\n        }\n    };\n    if (!isOpen) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [\n                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>onGenerate(config, optimizedPrompt),\n                    disabled: isGenerating,\n                    className: \"flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors\",\n                    children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成中...\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"生成图像\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>setIsOpen(true),\n                    className: \"flex items-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\",\n                    title: \"图像生成设置\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"设置\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 104,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-b border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold text-gray-900 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"图像生成设置\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsOpen(false),\n                                className: \"text-gray-500 hover:text-gray-700\",\n                                children: \"✕\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 space-y-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                    value: customPrompt,\n                                    onChange: (e)=>setCustomPrompt(e.target.value),\n                                    placeholder: optimizedPrompt || \"输入图像描述...\",\n                                    className: \"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none\",\n                                    rows: 3\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 13\n                                }, this),\n                                optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCustomPrompt(optimizedPrompt),\n                                    className: \"mt-2 text-sm text-blue-600 hover:text-blue-800\",\n                                    children: \"使用优化后的提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"生成模型\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-3\",\n                                    children: Object.entries(availableModels.models || {}).map((param)=>{\n                                        let [key, model] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    model: key\n                                                }),\n                                            className: \"p-3 border rounded-lg cursor-pointer transition-colors \".concat(config.model === key ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium text-gray-900\",\n                                                            children: model.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs rounded \".concat(getSpeedColor(model.speed)),\n                                                                    children: model.speed\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 199,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"px-2 py-1 text-xs rounded \".concat(getQualityColor(model.quality)),\n                                                                    children: model.quality\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: model.description\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"图像尺寸\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: Object.entries(availableModels.sizes || {}).map((param)=>{\n                                        let [key, size] = param;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setConfig({\n                                                    ...config,\n                                                    size: key\n                                                }),\n                                            className: \"p-2 text-sm border rounded transition-colors \".concat(config.size === key ? 'border-blue-500 bg-blue-50 text-blue-700' : 'border-gray-200 hover:border-gray-300'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: key\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 229,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: size\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, key, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 214,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                    children: [\n                                        \"生成数量: \",\n                                        config.num_images\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 238,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: \"1\",\n                                    max: \"4\",\n                                    value: config.num_images,\n                                    onChange: (e)=>setConfig({\n                                            ...config,\n                                            num_images: parseInt(e.target.value)\n                                        }),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 241,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"1张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"4张\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 251,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"border border-gray-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"p-3 cursor-pointer flex items-center space-x-2 hover:bg-gray-50\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: \"高级设置\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 border-t border-gray-200 space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: [\n                                                        \"引导强度: \",\n                                                        config.guidance_scale\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"range\",\n                                                    min: \"1\",\n                                                    max: \"20\",\n                                                    step: \"0.5\",\n                                                    value: config.guidance_scale,\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            guidance_scale: parseFloat(e.target.value)\n                                                        }),\n                                                    className: \"w-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"创意\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 277,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"精确\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 263,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"推理步数 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 284,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"100\",\n                                                    value: config.num_inference_steps || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            num_inference_steps: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"使用默认值\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 287,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"随机种子 (可选)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"number\",\n                                                    value: config.seed || '',\n                                                    onChange: (e)=>setConfig({\n                                                            ...config,\n                                                            seed: e.target.value ? parseInt(e.target.value) : undefined\n                                                        }),\n                                                    placeholder: \"随机生成\",\n                                                    className: \"w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                    lineNumber: 261,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 border-t border-gray-200 flex justify-end space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsOpen(false),\n                            className: \"px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\",\n                            children: \"取消\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerate,\n                            disabled: isGenerating || !customPrompt.trim(),\n                            className: \"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"生成中...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_CogIcon_PhotoIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"开始生成\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                            lineNumber: 329,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n                    lineNumber: 322,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/components/Chat/ImageGenerationPanel.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(ImageGenerationPanel, \"shjY+DSAkfeiVAbRAqcnBor6wI0=\");\n_c = ImageGenerationPanel;\nvar _c;\n$RefreshReg$(_c, \"ImageGenerationPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0NoYXQvSW1hZ2VHZW5lcmF0aW9uUGFuZWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFNZDtBQWtCdEIsU0FBU08scUJBQXFCLEtBSWpCO1FBSmlCLEVBQzNDQyxVQUFVLEVBQ1ZDLFlBQVksRUFDWkMsZUFBZSxFQUNXLEdBSmlCOztJQUszQyxNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR1gsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDWSxRQUFRQyxVQUFVLEdBQUdiLCtDQUFRQSxDQUF3QjtRQUMxRGMsT0FBTztRQUNQQyxNQUFNO1FBQ05DLFlBQVk7UUFDWkMsZ0JBQWdCO0lBQ2xCO0lBQ0EsTUFBTSxDQUFDQyxpQkFBaUJDLG1CQUFtQixHQUFHbkIsK0NBQVFBLENBQU0sQ0FBQztJQUM3RCxNQUFNLENBQUNvQixjQUFjQyxnQkFBZ0IsR0FBR3JCLCtDQUFRQSxDQUFDO0lBRWpELFlBQVk7SUFDWkMsZ0RBQVNBOzBDQUFDO1lBQ1IsTUFBTXFCOzZEQUFhO29CQUNqQixJQUFJO3dCQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTTt3QkFDN0IsSUFBSUQsU0FBU0UsRUFBRSxFQUFFOzRCQUNmLE1BQU1DLE9BQU8sTUFBTUgsU0FBU0ksSUFBSTs0QkFDaENSLG1CQUFtQk87d0JBQ3JCO29CQUNGLEVBQUUsT0FBT0UsT0FBTzt3QkFDZEMsUUFBUUQsS0FBSyxDQUFDLDJDQUEyQ0E7b0JBQzNEO2dCQUNGOztZQUNBTjtRQUNGO3lDQUFHLEVBQUU7SUFFTCxlQUFlO0lBQ2ZyQixnREFBU0E7MENBQUM7WUFDUixJQUFJUSxtQkFBbUIsQ0FBQ1csY0FBYztnQkFDcENDLGdCQUFnQlo7WUFDbEI7UUFDRjt5Q0FBRztRQUFDQTtRQUFpQlc7S0FBYTtJQUVsQyxNQUFNVSxpQkFBaUI7UUFDckIsTUFBTUMsY0FBY1gsZ0JBQWdCWCxtQkFBbUI7UUFDdkQsSUFBSSxDQUFDc0IsWUFBWUMsSUFBSSxJQUFJO1lBQ3ZCQyxNQUFNO1lBQ047UUFDRjtRQUNBMUIsV0FBV0ssUUFBUW1CO1FBQ25CcEIsVUFBVTtJQUNaO0lBRUEsTUFBTXVCLGVBQWUsQ0FBQ0M7WUFDYmpCO1FBQVAsT0FBT0EsRUFBQUEsMEJBQUFBLGdCQUFnQmtCLE1BQU0sY0FBdEJsQiw4Q0FBQUEsdUJBQXdCLENBQUNpQixTQUFTLEtBQUk7WUFDM0NFLE1BQU1GO1lBQ05HLGFBQWE7WUFDYkMsT0FBTztZQUNQQyxTQUFTO1FBQ1g7SUFDRjtJQUVBLE1BQU1DLGdCQUFnQixDQUFDRjtRQUNyQixPQUFRQTtZQUNOLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFVLE9BQU87WUFDdEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1HLGtCQUFrQixDQUFDRjtRQUN2QixPQUFRQTtZQUNOLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFRLE9BQU87WUFDcEIsS0FBSztnQkFBUSxPQUFPO1lBQ3BCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLElBQUksQ0FBQzlCLFFBQVE7UUFDWCxxQkFDRSw4REFBQ2lDO1lBQUlDLFdBQVU7O2dCQUNabkMsaUNBQ0MsOERBQUNvQztvQkFDQ0MsU0FBUyxJQUFNdkMsV0FBV0ssUUFBUUg7b0JBQ2xDc0MsVUFBVXZDO29CQUNWb0MsV0FBVTs4QkFFVHBDLDZCQUNDOzswQ0FDRSw4REFBQ21DO2dDQUFJQyxXQUFVOzs7Ozs7MENBQ2YsOERBQUNJOzBDQUFLOzs7Ozs7O3FEQUdSOzswQ0FDRSw4REFBQzdDLGtKQUFTQTtnQ0FBQ3lDLFdBQVU7Ozs7OzswQ0FDckIsOERBQUNJOzBDQUFLOzs7Ozs7Ozs7Ozs7OzhCQU1kLDhEQUFDSDtvQkFDQ0MsU0FBUyxJQUFNbkMsVUFBVTtvQkFDekJpQyxXQUFVO29CQUNWSyxPQUFNOztzQ0FFTiw4REFBQy9DLGtKQUFPQTs0QkFBQzBDLFdBQVU7Ozs7OztzQ0FDbkIsOERBQUNJO3NDQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJZDtJQUVBLHFCQUNFLDhEQUFDTDtRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ007Z0NBQUdOLFdBQVU7O2tEQUNaLDhEQUFDeEMsa0pBQVlBO3dDQUFDd0MsV0FBVTs7Ozs7O2tEQUN4Qiw4REFBQ0k7a0RBQUs7Ozs7Ozs7Ozs7OzswQ0FFUiw4REFBQ0g7Z0NBQ0NDLFNBQVMsSUFBTW5DLFVBQVU7Z0NBQ3pCaUMsV0FBVTswQ0FDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBT0wsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7OzhDQUNDLDhEQUFDUTtvQ0FBTVAsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNRO29DQUNDQyxPQUFPakM7b0NBQ1BrQyxVQUFVLENBQUNDLElBQU1sQyxnQkFBZ0JrQyxFQUFFQyxNQUFNLENBQUNILEtBQUs7b0NBQy9DSSxhQUFhaEQsbUJBQW1CO29DQUNoQ21DLFdBQVU7b0NBQ1ZjLE1BQU07Ozs7OztnQ0FFUGpELGlDQUNDLDhEQUFDb0M7b0NBQ0NDLFNBQVMsSUFBTXpCLGdCQUFnQlo7b0NBQy9CbUMsV0FBVTs4Q0FDWDs7Ozs7Ozs7Ozs7O3NDQU9MLDhEQUFDRDs7OENBQ0MsOERBQUNRO29DQUFNUCxXQUFVOzhDQUErQzs7Ozs7OzhDQUdoRSw4REFBQ0Q7b0NBQUlDLFdBQVU7OENBQ1plLE9BQU9DLE9BQU8sQ0FBQzFDLGdCQUFnQmtCLE1BQU0sSUFBSSxDQUFDLEdBQUd5QixHQUFHLENBQUM7NENBQUMsQ0FBQ0MsS0FBS2hELE1BQXFCOzZEQUM1RSw4REFBQzZCOzRDQUVDRyxTQUFTLElBQU1qQyxVQUFVO29EQUFFLEdBQUdELE1BQU07b0RBQUVFLE9BQU9nRDtnREFBSTs0Q0FDakRsQixXQUFXLDBEQUlWLE9BSENoQyxPQUFPRSxLQUFLLEtBQUtnRCxNQUNiLCtCQUNBOzs4REFHTiw4REFBQ25CO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ21COzREQUFHbkIsV0FBVTtzRUFBNkI5QixNQUFNdUIsSUFBSTs7Ozs7O3NFQUNyRCw4REFBQ007NERBQUlDLFdBQVU7OzhFQUNiLDhEQUFDSTtvRUFBS0osV0FBVyw2QkFBd0QsT0FBM0JILGNBQWMzQixNQUFNeUIsS0FBSzs4RUFDcEV6QixNQUFNeUIsS0FBSzs7Ozs7OzhFQUVkLDhEQUFDUztvRUFBS0osV0FBVyw2QkFBNEQsT0FBL0JGLGdCQUFnQjVCLE1BQU0wQixPQUFPOzhFQUN4RTFCLE1BQU0wQixPQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OERBSXBCLDhEQUFDd0I7b0RBQUVwQixXQUFVOzhEQUF5QjlCLE1BQU13QixXQUFXOzs7Ozs7OzJDQW5CbER3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBMEJiLDhEQUFDbkI7OzhDQUNDLDhEQUFDUTtvQ0FBTVAsV0FBVTs4Q0FBK0M7Ozs7Ozs4Q0FHaEUsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNaZSxPQUFPQyxPQUFPLENBQUMxQyxnQkFBZ0IrQyxLQUFLLElBQUksQ0FBQyxHQUFHSixHQUFHLENBQUM7NENBQUMsQ0FBQ0MsS0FBSy9DLEtBQW9COzZEQUMxRSw4REFBQzhCOzRDQUVDQyxTQUFTLElBQU1qQyxVQUFVO29EQUFFLEdBQUdELE1BQU07b0RBQUVHLE1BQU0rQztnREFBSTs0Q0FDaERsQixXQUFXLGdEQUlWLE9BSENoQyxPQUFPRyxJQUFJLEtBQUsrQyxNQUNaLDZDQUNBOzs4REFHTiw4REFBQ25CO29EQUFJQyxXQUFVOzhEQUFla0I7Ozs7Ozs4REFDOUIsOERBQUNuQjtvREFBSUMsV0FBVTs4REFBeUI3Qjs7Ozs7OzsyQ0FUbkMrQzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBZ0JiLDhEQUFDbkI7OzhDQUNDLDhEQUFDUTtvQ0FBTVAsV0FBVTs7d0NBQStDO3dDQUN2RGhDLE9BQU9JLFVBQVU7Ozs7Ozs7OENBRTFCLDhEQUFDa0Q7b0NBQ0NDLE1BQUs7b0NBQ0xDLEtBQUk7b0NBQ0pDLEtBQUk7b0NBQ0poQixPQUFPekMsT0FBT0ksVUFBVTtvQ0FDeEJzQyxVQUFVLENBQUNDLElBQU0xQyxVQUFVOzRDQUFFLEdBQUdELE1BQU07NENBQUVJLFlBQVlzRCxTQUFTZixFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQUU7b0NBQzdFVCxXQUFVOzs7Ozs7OENBRVosOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0k7c0RBQUs7Ozs7OztzREFDTiw4REFBQ0E7c0RBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FLViw4REFBQ3VCOzRCQUFRM0IsV0FBVTs7OENBQ2pCLDhEQUFDNEI7b0NBQVE1QixXQUFVOztzREFDakIsOERBQUN2QyxrSkFBeUJBOzRDQUFDdUMsV0FBVTs7Ozs7O3NEQUNyQyw4REFBQ0k7NENBQUtKLFdBQVU7c0RBQWM7Ozs7Ozs7Ozs7Ozs4Q0FFaEMsOERBQUNEO29DQUFJQyxXQUFVOztzREFFYiw4REFBQ0Q7OzhEQUNDLDhEQUFDUTtvREFBTVAsV0FBVTs7d0RBQStDO3dEQUN2RGhDLE9BQU9LLGNBQWM7Ozs7Ozs7OERBRTlCLDhEQUFDaUQ7b0RBQ0NDLE1BQUs7b0RBQ0xDLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pJLE1BQUs7b0RBQ0xwQixPQUFPekMsT0FBT0ssY0FBYztvREFDNUJxQyxVQUFVLENBQUNDLElBQU0xQyxVQUFVOzREQUFFLEdBQUdELE1BQU07NERBQUVLLGdCQUFnQnlELFdBQVduQixFQUFFQyxNQUFNLENBQUNILEtBQUs7d0RBQUU7b0RBQ25GVCxXQUFVOzs7Ozs7OERBRVosOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ0k7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7c0VBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzREFLViw4REFBQ0w7OzhEQUNDLDhEQUFDUTtvREFBTVAsV0FBVTs4REFBK0M7Ozs7Ozs4REFHaEUsOERBQUNzQjtvREFDQ0MsTUFBSztvREFDTEMsS0FBSTtvREFDSkMsS0FBSTtvREFDSmhCLE9BQU96QyxPQUFPK0QsbUJBQW1CLElBQUk7b0RBQ3JDckIsVUFBVSxDQUFDQyxJQUFNMUMsVUFBVTs0REFDekIsR0FBR0QsTUFBTTs0REFDVCtELHFCQUFxQnBCLEVBQUVDLE1BQU0sQ0FBQ0gsS0FBSyxHQUFHaUIsU0FBU2YsRUFBRUMsTUFBTSxDQUFDSCxLQUFLLElBQUl1Qjt3REFDbkU7b0RBQ0FuQixhQUFZO29EQUNaYixXQUFVOzs7Ozs7Ozs7Ozs7c0RBS2QsOERBQUNEOzs4REFDQyw4REFBQ1E7b0RBQU1QLFdBQVU7OERBQStDOzs7Ozs7OERBR2hFLDhEQUFDc0I7b0RBQ0NDLE1BQUs7b0RBQ0xkLE9BQU96QyxPQUFPaUUsSUFBSSxJQUFJO29EQUN0QnZCLFVBQVUsQ0FBQ0MsSUFBTTFDLFVBQVU7NERBQ3pCLEdBQUdELE1BQU07NERBQ1RpRSxNQUFNdEIsRUFBRUMsTUFBTSxDQUFDSCxLQUFLLEdBQUdpQixTQUFTZixFQUFFQyxNQUFNLENBQUNILEtBQUssSUFBSXVCO3dEQUNwRDtvREFDQW5CLGFBQVk7b0RBQ1piLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRcEIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQ0NDLFNBQVMsSUFBTW5DLFVBQVU7NEJBQ3pCaUMsV0FBVTtzQ0FDWDs7Ozs7O3NDQUdELDhEQUFDQzs0QkFDQ0MsU0FBU2hCOzRCQUNUaUIsVUFBVXZDLGdCQUFnQixDQUFDWSxhQUFhWSxJQUFJOzRCQUM1Q1ksV0FBVTtzQ0FFVHBDLDZCQUNDOztrREFDRSw4REFBQ21DO3dDQUFJQyxXQUFVOzs7Ozs7a0RBQ2YsOERBQUNJO2tEQUFLOzs7Ozs7OzZEQUdSOztrREFDRSw4REFBQzdDLGtKQUFTQTt3Q0FBQ3lDLFdBQVU7Ozs7OztrREFDckIsOERBQUNJO2tEQUFLOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRdEI7R0FuVXdCMUM7S0FBQUEiLCJzb3VyY2VzIjpbIi9Vc2Vycy9saXVwZW5nL2N1cnNvci/ov5vpmLYvRmx1eOWvueivneW8j+W6lOeUqC9mbHV4LWNoYXQtYXBwL3NyYy9jb21wb25lbnRzL0NoYXQvSW1hZ2VHZW5lcmF0aW9uUGFuZWwudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBcbiAgQ29nSWNvbixcbiAgUGhvdG9JY29uLFxuICBTcGFya2xlc0ljb24sXG4gIEFkanVzdG1lbnRzSG9yaXpvbnRhbEljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIEltYWdlR2VuZXJhdGlvbkNvbmZpZyB7XG4gIG1vZGVsOiBzdHJpbmc7XG4gIHNpemU6IHN0cmluZztcbiAgbnVtX2ltYWdlczogbnVtYmVyO1xuICBzdHlsZV9wcmVzZXQ/OiBzdHJpbmc7XG4gIGd1aWRhbmNlX3NjYWxlOiBudW1iZXI7XG4gIG51bV9pbmZlcmVuY2Vfc3RlcHM/OiBudW1iZXI7XG4gIHNlZWQ/OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBJbWFnZUdlbmVyYXRpb25QYW5lbFByb3BzIHtcbiAgb25HZW5lcmF0ZTogKGNvbmZpZzogSW1hZ2VHZW5lcmF0aW9uQ29uZmlnLCBwcm9tcHQ6IHN0cmluZykgPT4gdm9pZDtcbiAgaXNHZW5lcmF0aW5nOiBib29sZWFuO1xuICBvcHRpbWl6ZWRQcm9tcHQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEltYWdlR2VuZXJhdGlvblBhbmVsKHsgXG4gIG9uR2VuZXJhdGUsIFxuICBpc0dlbmVyYXRpbmcsIFxuICBvcHRpbWl6ZWRQcm9tcHQgXG59OiBJbWFnZUdlbmVyYXRpb25QYW5lbFByb3BzKSB7XG4gIGNvbnN0IFtpc09wZW4sIHNldElzT3Blbl0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtjb25maWcsIHNldENvbmZpZ10gPSB1c2VTdGF0ZTxJbWFnZUdlbmVyYXRpb25Db25maWc+KHtcbiAgICBtb2RlbDogJ2ZsdXgtc2NobmVsbCcsXG4gICAgc2l6ZTogJ2xhbmRzY2FwZScsXG4gICAgbnVtX2ltYWdlczogMSxcbiAgICBndWlkYW5jZV9zY2FsZTogNy41LFxuICB9KTtcbiAgY29uc3QgW2F2YWlsYWJsZU1vZGVscywgc2V0QXZhaWxhYmxlTW9kZWxzXSA9IHVzZVN0YXRlPGFueT4oe30pO1xuICBjb25zdCBbY3VzdG9tUHJvbXB0LCBzZXRDdXN0b21Qcm9tcHRdID0gdXNlU3RhdGUoJycpO1xuXG4gIC8vIOWKoOi9veWPr+eUqOaooeWei+WSjOmFjee9rlxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGxvYWRDb25maWcgPSBhc3luYyAoKSA9PiB7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2dlbmVyYXRlLWltYWdlJyk7XG4gICAgICAgIGlmIChyZXNwb25zZS5vaykge1xuICAgICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgc2V0QXZhaWxhYmxlTW9kZWxzKGRhdGEpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBpbWFnZSBnZW5lcmF0aW9uIGNvbmZpZzonLCBlcnJvcik7XG4gICAgICB9XG4gICAgfTtcbiAgICBsb2FkQ29uZmlnKCk7XG4gIH0sIFtdKTtcblxuICAvLyDlvZPmnInkvJjljJbmj5DnpLror43ml7boh6rliqjloavlhYVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAob3B0aW1pemVkUHJvbXB0ICYmICFjdXN0b21Qcm9tcHQpIHtcbiAgICAgIHNldEN1c3RvbVByb21wdChvcHRpbWl6ZWRQcm9tcHQpO1xuICAgIH1cbiAgfSwgW29wdGltaXplZFByb21wdCwgY3VzdG9tUHJvbXB0XSk7XG5cbiAgY29uc3QgaGFuZGxlR2VuZXJhdGUgPSAoKSA9PiB7XG4gICAgY29uc3QgcHJvbXB0VG9Vc2UgPSBjdXN0b21Qcm9tcHQgfHwgb3B0aW1pemVkUHJvbXB0IHx8ICcnO1xuICAgIGlmICghcHJvbXB0VG9Vc2UudHJpbSgpKSB7XG4gICAgICBhbGVydCgn6K+36L6T5YWl5o+Q56S66K+N5oiW5YWI5LyY5YyW5a+56K+d5Lit55qE5o+Q56S66K+NJyk7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIG9uR2VuZXJhdGUoY29uZmlnLCBwcm9tcHRUb1VzZSk7XG4gICAgc2V0SXNPcGVuKGZhbHNlKTtcbiAgfTtcblxuICBjb25zdCBnZXRNb2RlbEluZm8gPSAobW9kZWxLZXk6IHN0cmluZykgPT4ge1xuICAgIHJldHVybiBhdmFpbGFibGVNb2RlbHMubW9kZWxzPy5bbW9kZWxLZXldIHx8IHsgXG4gICAgICBuYW1lOiBtb2RlbEtleSwgXG4gICAgICBkZXNjcmlwdGlvbjogJ+acquefpeaooeWeiycsXG4gICAgICBzcGVlZDogJ21lZGl1bScsXG4gICAgICBxdWFsaXR5OiAnZ29vZCdcbiAgICB9O1xuICB9O1xuXG4gIGNvbnN0IGdldFNwZWVkQ29sb3IgPSAoc3BlZWQ6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3BlZWQpIHtcbiAgICAgIGNhc2UgJ2Zhc3QnOiByZXR1cm4gJ3RleHQtZ3JlZW4tNjAwIGJnLWdyZWVuLTUwJztcbiAgICAgIGNhc2UgJ21lZGl1bSc6IHJldHVybiAndGV4dC15ZWxsb3ctNjAwIGJnLXllbGxvdy01MCc7XG4gICAgICBjYXNlICdzbG93JzogcmV0dXJuICd0ZXh0LXJlZC02MDAgYmctcmVkLTUwJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAndGV4dC1ncmF5LTYwMCBiZy1ncmF5LTUwJztcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgZ2V0UXVhbGl0eUNvbG9yID0gKHF1YWxpdHk6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAocXVhbGl0eSkge1xuICAgICAgY2FzZSAnZXhjZWxsZW50JzogcmV0dXJuICd0ZXh0LXB1cnBsZS02MDAgYmctcHVycGxlLTUwJztcbiAgICAgIGNhc2UgJ2hpZ2gnOiByZXR1cm4gJ3RleHQtYmx1ZS02MDAgYmctYmx1ZS01MCc7XG4gICAgICBjYXNlICdnb29kJzogcmV0dXJuICd0ZXh0LWdyZWVuLTYwMCBiZy1ncmVlbi01MCc7XG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ3RleHQtZ3JheS02MDAgYmctZ3JheS01MCc7XG4gICAgfVxuICB9O1xuXG4gIGlmICghaXNPcGVuKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgIHtvcHRpbWl6ZWRQcm9tcHQgJiYgKFxuICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uR2VuZXJhdGUoY29uZmlnLCBvcHRpbWl6ZWRQcm9tcHQpfVxuICAgICAgICAgICAgZGlzYWJsZWQ9e2lzR2VuZXJhdGluZ31cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC00IHB5LTIgYmctYmx1ZS01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWxnIGhvdmVyOmJnLWJsdWUtNjAwIGRpc2FibGVkOmJnLWdyYXktNDAwIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzR2VuZXJhdGluZyA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIDxzcGFuPueUn+aIkOS4rS4uLjwvc3Bhbj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxQaG90b0ljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4+55Sf5oiQ5Zu+5YOPPC9zcGFuPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICl9XG4gICAgICAgIFxuICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKHRydWUpfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBweC0zIHB5LTIgYmctZ3JheS0xMDAgdGV4dC1ncmF5LTcwMCByb3VuZGVkLWxnIGhvdmVyOmJnLWdyYXktMjAwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICB0aXRsZT1cIuWbvuWDj+eUn+aIkOiuvue9rlwiXG4gICAgICAgID5cbiAgICAgICAgICA8Q29nSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICA8c3Bhbj7orr7nva48L3NwYW4+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNTAgei00MCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBwLTRcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3cteGwgdy1mdWxsIG1heC13LTJ4bCBtYXgtaC1bOTB2aF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgIHsvKiDlpLTpg6ggKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDAgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwidy02IGgtNiB0ZXh0LWJsdWUtNTAwXCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4+5Zu+5YOP55Sf5oiQ6K6+572uPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0SXNPcGVuKGZhbHNlKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTUwMCBob3Zlcjp0ZXh0LWdyYXktNzAwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAg4pyVXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIOWGheWuuSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgc3BhY2UteS02XCI+XG4gICAgICAgICAgey8qIOaPkOekuuivjee8lui+kSAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIOaPkOekuuivjVxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICB2YWx1ZT17Y3VzdG9tUHJvbXB0fVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldEN1c3RvbVByb21wdChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtvcHRpbWl6ZWRQcm9tcHQgfHwgXCLovpPlhaXlm77lg4/mj4/ov7AuLi5cIn1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbGcgZm9jdXM6b3V0bGluZS1ub25lIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIHJlc2l6ZS1ub25lXCJcbiAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7b3B0aW1pemVkUHJvbXB0ICYmIChcbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldEN1c3RvbVByb21wdChvcHRpbWl6ZWRQcm9tcHQpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDBcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAg5L2/55So5LyY5YyW5ZCO55qE5o+Q56S66K+NXG4gICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIHsvKiDmqKHlnovpgInmi6kgKi99XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICDnlJ/miJDmqKHlnotcbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgZ2FwLTNcIj5cbiAgICAgICAgICAgICAge09iamVjdC5lbnRyaWVzKGF2YWlsYWJsZU1vZGVscy5tb2RlbHMgfHwge30pLm1hcCgoW2tleSwgbW9kZWxdOiBbc3RyaW5nLCBhbnldKSA9PiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAga2V5PXtrZXl9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDb25maWcoeyAuLi5jb25maWcsIG1vZGVsOiBrZXkgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BwLTMgYm9yZGVyIHJvdW5kZWQtbGcgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgY29uZmlnLm1vZGVsID09PSBrZXlcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCdcbiAgICAgICAgICAgICAgICAgICAgICA6ICdib3JkZXItZ3JheS0yMDAgaG92ZXI6Ym9yZGVyLWdyYXktMzAwJ1xuICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItMVwiPlxuICAgICAgICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gdGV4dC1ncmF5LTkwMFwiPnttb2RlbC5uYW1lfTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2BweC0yIHB5LTEgdGV4dC14cyByb3VuZGVkICR7Z2V0U3BlZWRDb2xvcihtb2RlbC5zcGVlZCl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bW9kZWwuc3BlZWR9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHB4LTIgcHktMSB0ZXh0LXhzIHJvdW5kZWQgJHtnZXRRdWFsaXR5Q29sb3IobW9kZWwucXVhbGl0eSl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7bW9kZWwucXVhbGl0eX1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDBcIj57bW9kZWwuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOWbvuWDj+WwuuWvuCAqL31cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgIOWbvuWDj+WwuuWvuFxuICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtMlwiPlxuICAgICAgICAgICAgICB7T2JqZWN0LmVudHJpZXMoYXZhaWxhYmxlTW9kZWxzLnNpemVzIHx8IHt9KS5tYXAoKFtrZXksIHNpemVdOiBbc3RyaW5nLCBhbnldKSA9PiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAga2V5PXtrZXl9XG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRDb25maWcoeyAuLi5jb25maWcsIHNpemU6IGtleSB9KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHAtMiB0ZXh0LXNtIGJvcmRlciByb3VuZGVkIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGNvbmZpZy5zaXplID09PSBrZXlcbiAgICAgICAgICAgICAgICAgICAgICA/ICdib3JkZXItYmx1ZS01MDAgYmctYmx1ZS01MCB0ZXh0LWJsdWUtNzAwJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JvcmRlci1ncmF5LTIwMCBob3Zlcjpib3JkZXItZ3JheS0zMDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2tleX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+e3NpemV9PC9kaXY+XG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7Lyog55Sf5oiQ5pWw6YePICovfVxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAg55Sf5oiQ5pWw6YePOiB7Y29uZmlnLm51bV9pbWFnZXN9XG4gICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICBtYXg9XCI0XCJcbiAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5udW1faW1hZ2VzfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldENvbmZpZyh7IC4uLmNvbmZpZywgbnVtX2ltYWdlczogcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIH0pfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gdGV4dC14cyB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgPHNwYW4+MeW8oDwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4+NOW8oDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOmrmOe6p+iuvue9riAqL31cbiAgICAgICAgICA8ZGV0YWlscyBjbGFzc05hbWU9XCJib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgIDxzdW1tYXJ5IGNsYXNzTmFtZT1cInAtMyBjdXJzb3ItcG9pbnRlciBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgaG92ZXI6YmctZ3JheS01MFwiPlxuICAgICAgICAgICAgICA8QWRqdXN0bWVudHNIb3Jpem9udGFsSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj7pq5jnuqforr7nva48L3NwYW4+XG4gICAgICAgICAgICA8L3N1bW1hcnk+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtMyBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgc3BhY2UteS00XCI+XG4gICAgICAgICAgICAgIHsvKiDlvJXlr7zlvLrluqYgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICDlvJXlr7zlvLrluqY6IHtjb25maWcuZ3VpZGFuY2Vfc2NhbGV9XG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJyYW5nZVwiXG4gICAgICAgICAgICAgICAgICBtaW49XCIxXCJcbiAgICAgICAgICAgICAgICAgIG1heD1cIjIwXCJcbiAgICAgICAgICAgICAgICAgIHN0ZXA9XCIwLjVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5ndWlkYW5jZV9zY2FsZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHsgLi4uY29uZmlnLCBndWlkYW5jZV9zY2FsZTogcGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGxcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiB0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+5Yib5oSPPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgPHNwYW4+57K+56GuPC9zcGFuPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7Lyog5o6o55CG5q2l5pWwICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgICAg5o6o55CG5q2l5pWwICjlj6/pgIkpXG4gICAgICAgICAgICAgICAgPC9sYWJlbD5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgICAgbWluPVwiMVwiXG4gICAgICAgICAgICAgICAgICBtYXg9XCIxMDBcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2NvbmZpZy5udW1faW5mZXJlbmNlX3N0ZXBzIHx8ICcnfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRDb25maWcoeyBcbiAgICAgICAgICAgICAgICAgICAgLi4uY29uZmlnLCBcbiAgICAgICAgICAgICAgICAgICAgbnVtX2luZmVyZW5jZV9zdGVwczogZS50YXJnZXQudmFsdWUgPyBwYXJzZUludChlLnRhcmdldC52YWx1ZSkgOiB1bmRlZmluZWQgXG4gICAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi5L2/55So6buY6K6k5YC8XCJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIOmaj+acuuenjeWtkCAqL31cbiAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiYmxvY2sgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIOmaj+acuuenjeWtkCAo5Y+v6YCJKVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtjb25maWcuc2VlZCB8fCAnJ31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0Q29uZmlnKHsgXG4gICAgICAgICAgICAgICAgICAgIC4uLmNvbmZpZywgXG4gICAgICAgICAgICAgICAgICAgIHNlZWQ6IGUudGFyZ2V0LnZhbHVlID8gcGFyc2VJbnQoZS50YXJnZXQudmFsdWUpIDogdW5kZWZpbmVkIFxuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIumaj+acuueUn+aIkFwiXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kZXRhaWxzPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5bqV6YOo5oyJ6ZKuICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNiBib3JkZXItdCBib3JkZXItZ3JheS0yMDAgZmxleCBqdXN0aWZ5LWVuZCBzcGFjZS14LTNcIj5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRJc09wZW4oZmFsc2UpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIHRleHQtZ3JheS03MDAgYmctZ3JheS0xMDAgcm91bmRlZC1sZyBob3ZlcjpiZy1ncmF5LTIwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgPlxuICAgICAgICAgICAg5Y+W5raIXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgb25DbGljaz17aGFuZGxlR2VuZXJhdGV9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nIHx8ICFjdXN0b21Qcm9tcHQudHJpbSgpfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTYwMCBkaXNhYmxlZDpiZy1ncmF5LTQwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbi1jb2xvcnMgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNHZW5lcmF0aW5nID8gKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTQgdy00IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgPHNwYW4+55Sf5oiQ5LitLi4uPC9zcGFuPlxuICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPFBob3RvSWNvbiBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICA8c3Bhbj7lvIDlp4vnlJ/miJA8L3NwYW4+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQ29nSWNvbiIsIlBob3RvSWNvbiIsIlNwYXJrbGVzSWNvbiIsIkFkanVzdG1lbnRzSG9yaXpvbnRhbEljb24iLCJJbWFnZUdlbmVyYXRpb25QYW5lbCIsIm9uR2VuZXJhdGUiLCJpc0dlbmVyYXRpbmciLCJvcHRpbWl6ZWRQcm9tcHQiLCJpc09wZW4iLCJzZXRJc09wZW4iLCJjb25maWciLCJzZXRDb25maWciLCJtb2RlbCIsInNpemUiLCJudW1faW1hZ2VzIiwiZ3VpZGFuY2Vfc2NhbGUiLCJhdmFpbGFibGVNb2RlbHMiLCJzZXRBdmFpbGFibGVNb2RlbHMiLCJjdXN0b21Qcm9tcHQiLCJzZXRDdXN0b21Qcm9tcHQiLCJsb2FkQ29uZmlnIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiZGF0YSIsImpzb24iLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVHZW5lcmF0ZSIsInByb21wdFRvVXNlIiwidHJpbSIsImFsZXJ0IiwiZ2V0TW9kZWxJbmZvIiwibW9kZWxLZXkiLCJtb2RlbHMiLCJuYW1lIiwiZGVzY3JpcHRpb24iLCJzcGVlZCIsInF1YWxpdHkiLCJnZXRTcGVlZENvbG9yIiwiZ2V0UXVhbGl0eUNvbG9yIiwiZGl2IiwiY2xhc3NOYW1lIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIiwic3BhbiIsInRpdGxlIiwiaDIiLCJsYWJlbCIsInRleHRhcmVhIiwidmFsdWUiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJwbGFjZWhvbGRlciIsInJvd3MiLCJPYmplY3QiLCJlbnRyaWVzIiwibWFwIiwia2V5IiwiaDQiLCJwIiwic2l6ZXMiLCJpbnB1dCIsInR5cGUiLCJtaW4iLCJtYXgiLCJwYXJzZUludCIsImRldGFpbHMiLCJzdW1tYXJ5Iiwic3RlcCIsInBhcnNlRmxvYXQiLCJudW1faW5mZXJlbmNlX3N0ZXBzIiwidW5kZWZpbmVkIiwic2VlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fliupeng%2Fcursor%2F%E8%BF%9B%E9%98%B6%2FFlux%E5%AF%B9%E8%AF%9D%E5%BC%8F%E5%BA%94%E7%94%A8%2Fflux-chat-app%2Fsrc%2Fapp%2Fchat%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);