"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* harmony import */ var _components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Chat/ImageUpload */ \"(app-pages-browser)/./src/components/Chat/ImageUpload.tsx\");\n/* harmony import */ var _components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Chat/ImageEditResult */ \"(app-pages-browser)/./src/components/Chat/ImageEditResult.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditingImage, setIsEditingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadedImages, setUploadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showImageUpload, setShowImageUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageEditor, setShowImageEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sendMessage = async ()=>{\n        if (!message.trim() && uploadedImages.length === 0) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message || '',\n            images: uploadedImages.length > 0 ? uploadedImages.map((img)=>({\n                    url: img.url,\n                    width: 400,\n                    height: 300,\n                    content_type: img.type\n                })) : undefined,\n            metadata: uploadedImages.length > 0 ? {\n                uploaded_images: uploadedImages,\n                image_count: uploadedImages.length\n            } : undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        // 清空上传的图片，避免重复发送\n        if (uploadedImages.length > 0) {\n            setUploadedImages([]);\n        }\n        // 如果用户只上传了图片没有文字，不调用AI接口\n        if (!userMessage.content && uploadedImages.length > 0) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversationHistory: messages.map((m)=>m.content)\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                // 检查是否需要自动生成提示词优化\n                if (!data.needsClarification && data.extractedRequirement) {\n                    await generateOptimizedPrompt();\n                }\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 生成优化提示词\n    const generateOptimizedPrompt = async ()=>{\n        try {\n            const conversationHistory = messages.map((m)=>m.content);\n            const requirement = conversationHistory.join(' ');\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement,\n                    conversationHistory,\n                    useAI: true\n                })\n            });\n            if (response.ok) {\n                var _data_optimization;\n                const data = await response.json();\n                if ((_data_optimization = data.optimization) === null || _data_optimization === void 0 ? void 0 : _data_optimization.optimizedPrompt) {\n                    setOptimizedPrompt(data.optimization.optimizedPrompt);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to generate optimized prompt:', error);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt)=>{\n        setIsGeneratingImage(true);\n        try {\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    prompt,\n                    ...config\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: \"已为您生成图像！使用了 \".concat(data.metadata.model_used, \" 模型。\"),\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : \"图像生成失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (images)=>{\n        setUploadedImages(images);\n    };\n    // 处理图片编辑\n    const handleImageEdit = async (image, instruction)=>{\n        setIsEditingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', image.file);\n            formData.append('instruction', instruction);\n            formData.append('model', 'instruct-pix2pix');\n            const response = await fetch('/api/edit-image', {\n                method: 'POST',\n                body: formData\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加编辑结果消息\n                const editMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: '已完成图片编辑！编辑指令: \"'.concat(instruction, '\"'),\n                    editResult: {\n                        originalImage: {\n                            url: image.url,\n                            name: image.name\n                        },\n                        editedImage: data.edited_image,\n                        metadata: data.metadata\n                    }\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        editMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图片编辑功能需要配置 FAL API 密钥。请查看配置说明。' : \"图片编辑失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图片编辑请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsEditingImage(false);\n        }\n    };\n    // 重新编辑图片\n    const handleReEdit = async (editResult, newInstruction)=>{\n        // 创建临时的UploadedImage对象\n        const tempImage = {\n            id: 'temp-' + Date.now(),\n            file: new File([], editResult.originalImage.name),\n            url: editResult.originalImage.url,\n            name: editResult.originalImage.name,\n            size: 0,\n            type: 'image/jpeg'\n        };\n        await handleImageEdit(tempImage, newInstruction);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"AI图像生成助手\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600\",\n                                    children: \"让我帮您优化图像生成提示词\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 285,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>{\n                                setMessages([]);\n                                setOptimizedPrompt('');\n                                setUploadedImages([]);\n                            },\n                            className: \"px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors\",\n                            children: \"清空对话\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"欢迎使用AI图像生成助手\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"请描述您想要生成的图像，我会帮您优化提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 307,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 305,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>{\n                            var _msg_metadata;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(msg.role === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] \".concat(msg.role === 'user' ? '' : 'w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg px-4 py-2 \".concat(msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'),\n                                            children: [\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 37\n                                                }, this),\n                                                msg.role === 'user' && ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.uploaded_images) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 grid grid-cols-2 gap-2\",\n                                                    children: msg.metadata.uploaded_images.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: img.url,\n                                                                alt: \"上传的图片 \".concat(idx + 1),\n                                                                className: \"w-full h-auto rounded-lg border border-white/20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                                lineNumber: 326,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, idx, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    var _msg_metadata;\n                                                    if ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.prompt_used) {\n                                                        var _msg_metadata_parameters, _msg_metadata_parameters1;\n                                                        handleImageGeneration({\n                                                            model: ((_msg_metadata_parameters = msg.metadata.parameters) === null || _msg_metadata_parameters === void 0 ? void 0 : _msg_metadata_parameters.model) || 'flux-schnell',\n                                                            size: ((_msg_metadata_parameters1 = msg.metadata.parameters) === null || _msg_metadata_parameters1 === void 0 ? void 0 : _msg_metadata_parameters1.size) || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 21\n                                        }, this),\n                                        msg.editResult && msg.editResult.editedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                originalImage: msg.editResult.originalImage,\n                                                editedImage: msg.editResult.editedImage,\n                                                metadata: msg.editResult.metadata,\n                                                onReEdit: (newInstruction)=>{\n                                                    handleReEdit(msg.editResult, newInstruction);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 377,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 303,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        showImageUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"上传图片进行编辑\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageUpload(false),\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 396,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onImageUpload: handleImageUpload,\n                                    onImageEdit: handleImageEdit,\n                                    maxFiles: 3,\n                                    maxSize: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 395,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: handleImageGeneration,\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, this),\n                        uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-3 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: [\n                                                \"准备发送的图片 (\",\n                                                uploadedImages.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUploadedImages([]),\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"清空\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: uploadedImages.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: img.url,\n                                                    alt: \"预览 \".concat(idx + 1),\n                                                    className: \"w-full h-20 object-cover rounded border\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setUploadedImages((prev)=>prev.filter((_, i)=>i !== idx));\n                                                    },\n                                                    className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 427,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowImageUpload(!showImageUpload),\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                                    title: \"上传图片进行编辑\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"图片\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您想要生成的图像，或上传图片进行编辑...\",\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 475,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && uploadedImages.length === 0 || isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图像生成示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 496,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            '可爱的小猫',\n                                            '未来城市',\n                                            '油画风景',\n                                            '卡通人物',\n                                            '抽象艺术'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setMessage(prompt),\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 13\n                                }, this),\n                                uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图片编辑示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            '改为黑白风格',\n                                            '添加蓝天背景',\n                                            '移除背景',\n                                            '调整为夜晚场景',\n                                            '添加彩色滤镜'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (uploadedImages.length > 0) {\n                                                        handleImageEdit(uploadedImages[0], prompt);\n                                                    }\n                                                },\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-green-50 text-green-600 rounded-full hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 392,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 391,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"khvJihUCBOhJ5VNz7pkb/Xea7Jk=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});