"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Chat/ImageDisplay */ \"(app-pages-browser)/./src/components/Chat/ImageDisplay.tsx\");\n/* harmony import */ var _components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/Chat/ImageGenerationPanel */ \"(app-pages-browser)/./src/components/Chat/ImageGenerationPanel.tsx\");\n/* harmony import */ var _components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Chat/ImageUpload */ \"(app-pages-browser)/./src/components/Chat/ImageUpload.tsx\");\n/* harmony import */ var _components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/Chat/ImageEditResult */ \"(app-pages-browser)/./src/components/Chat/ImageEditResult.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction ChatPage() {\n    _s();\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isGeneratingImage, setIsGeneratingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditingImage, setIsEditingImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [optimizedPrompt, setOptimizedPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [uploadedImages, setUploadedImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showImageUpload, setShowImageUpload] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showImageEditor, setShowImageEditor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const sendMessage = async ()=>{\n        if (!message.trim() && uploadedImages.length === 0) return;\n        const userMessage = {\n            id: Date.now().toString(),\n            role: 'user',\n            content: message || '',\n            images: uploadedImages.length > 0 ? uploadedImages.map((img)=>({\n                    url: img.url,\n                    width: 400,\n                    height: 300,\n                    content_type: img.type\n                })) : undefined,\n            metadata: uploadedImages.length > 0 ? {\n                uploaded_images: uploadedImages,\n                image_count: uploadedImages.length\n            } : undefined\n        };\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setMessage('');\n        // 清空上传的图片，避免重复发送\n        if (uploadedImages.length > 0) {\n            setUploadedImages([]);\n        }\n        // 如果用户只上传了图片没有文字，不调用AI接口\n        if (!userMessage.content && uploadedImages.length > 0) {\n            return;\n        }\n        setIsLoading(true);\n        try {\n            const response = await fetch('/api/chat', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    message: userMessage.content,\n                    conversationHistory: messages.map((m)=>m.content)\n                })\n            });\n            const data = await response.json();\n            if (response.ok) {\n                const assistantMessage = {\n                    id: (Date.now() + 1).toString(),\n                    role: 'assistant',\n                    content: data.response\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        assistantMessage\n                    ]);\n                // 检查是否需要自动生成提示词优化\n                if (!data.needsClarification && data.extractedRequirement) {\n                    await generateOptimizedPrompt();\n                }\n            } else {\n                setMessages((prev)=>[\n                        ...prev,\n                        {\n                            id: (Date.now() + 1).toString(),\n                            role: 'assistant',\n                            content: '抱歉，发生了错误。请稍后重试。'\n                        }\n                    ]);\n            }\n        } catch (error) {\n            setMessages((prev)=>[\n                    ...prev,\n                    {\n                        id: (Date.now() + 1).toString(),\n                        role: 'assistant',\n                        content: '网络错误，请检查连接。'\n                    }\n                ]);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // 生成优化提示词\n    const generateOptimizedPrompt = async ()=>{\n        try {\n            const conversationHistory = messages.map((m)=>m.content);\n            const requirement = conversationHistory.join(' ');\n            const response = await fetch('/api/optimize-prompt', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    requirement,\n                    conversationHistory,\n                    useAI: true\n                })\n            });\n            if (response.ok) {\n                var _data_optimization;\n                const data = await response.json();\n                if ((_data_optimization = data.optimization) === null || _data_optimization === void 0 ? void 0 : _data_optimization.optimizedPrompt) {\n                    setOptimizedPrompt(data.optimization.optimizedPrompt);\n                }\n            }\n        } catch (error) {\n            console.error('Failed to generate optimized prompt:', error);\n        }\n    };\n    // 处理图像生成\n    const handleImageGeneration = async (config, prompt)=>{\n        setIsGeneratingImage(true);\n        try {\n            const response = await fetch('/api/generate-image', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    prompt,\n                    ...config\n                })\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加图像消息\n                const imageMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: \"已为您生成图像！使用了 \".concat(data.metadata.model_used, \" 模型。\"),\n                    images: data.images,\n                    metadata: data.metadata\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        imageMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图像生成功能需要配置 FAL API 密钥。请查看配置说明。' : \"图像生成失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图像生成请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsGeneratingImage(false);\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = (images)=>{\n        setUploadedImages(images);\n    };\n    // 处理图片编辑\n    const handleImageEdit = async (image, instruction)=>{\n        setIsEditingImage(true);\n        try {\n            const formData = new FormData();\n            formData.append('image', image.file);\n            formData.append('instruction', instruction);\n            formData.append('model', 'instruct-pix2pix');\n            const response = await fetch('/api/edit-image', {\n                method: 'POST',\n                body: formData\n            });\n            const data = await response.json();\n            if (response.ok && data.success) {\n                // 添加编辑结果消息\n                const editMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: '已完成图片编辑！编辑指令: \"'.concat(instruction, '\"'),\n                    editResult: {\n                        originalImage: {\n                            url: image.url,\n                            name: image.name\n                        },\n                        editedImage: data.edited_image,\n                        metadata: data.metadata\n                    }\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        editMessage\n                    ]);\n            } else {\n                // 处理错误\n                const errorMessage = {\n                    id: Date.now().toString(),\n                    role: 'assistant',\n                    content: data.demo_mode ? '图片编辑功能需要配置 FAL API 密钥。请查看配置说明。' : \"图片编辑失败: \".concat(data.error || '未知错误')\n                };\n                setMessages((prev)=>[\n                        ...prev,\n                        errorMessage\n                    ]);\n            }\n        } catch (error) {\n            const errorMessage = {\n                id: Date.now().toString(),\n                role: 'assistant',\n                content: '图片编辑请求失败，请检查网络连接。'\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    errorMessage\n                ]);\n        } finally{\n            setIsEditingImage(false);\n        }\n    };\n    // 重新编辑图片\n    const handleReEdit = async (editResult, newInstruction)=>{\n        // 创建临时的UploadedImage对象\n        const tempImage = {\n            id: 'temp-' + Date.now(),\n            file: new File([], editResult.originalImage.name),\n            url: editResult.originalImage.url,\n            name: editResult.originalImage.name,\n            size: 0,\n            type: 'image/jpeg'\n        };\n        await handleImageEdit(tempImage, newInstruction);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-xl font-semibold text-gray-900\",\n                        children: \"AI图像生成助手\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-600\",\n                        children: \"让我帮您优化图像生成提示词\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4\",\n                children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center text-gray-500 mt-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium\",\n                            children: \"欢迎使用AI图像生成助手\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm mt-2\",\n                            children: \"请描述您想要生成的图像，我会帮您优化提示词\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 289,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 max-w-4xl mx-auto\",\n                    children: [\n                        messages.map((msg)=>{\n                            var _msg_metadata;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex \".concat(msg.role === 'user' ? 'justify-end' : 'justify-start'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[80%] \".concat(msg.role === 'user' ? '' : 'w-full'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"rounded-lg px-4 py-2 \".concat(msg.role === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-900 border border-gray-200'),\n                                            children: [\n                                                msg.content && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: msg.content\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 37\n                                                }, this),\n                                                msg.role === 'user' && ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.uploaded_images) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-3 grid grid-cols-2 gap-2\",\n                                                    children: msg.metadata.uploaded_images.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: img.url,\n                                                                alt: \"上传的图片 \".concat(idx + 1),\n                                                                className: \"w-full h-auto rounded-lg border border-white/20\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, idx, false, {\n                                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 27\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 19\n                                        }, this),\n                                        msg.images && msg.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageDisplay__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                images: msg.images,\n                                                metadata: msg.metadata,\n                                                onRegenerate: (seed)=>{\n                                                    var _msg_metadata;\n                                                    if ((_msg_metadata = msg.metadata) === null || _msg_metadata === void 0 ? void 0 : _msg_metadata.prompt_used) {\n                                                        var _msg_metadata_parameters, _msg_metadata_parameters1;\n                                                        handleImageGeneration({\n                                                            model: ((_msg_metadata_parameters = msg.metadata.parameters) === null || _msg_metadata_parameters === void 0 ? void 0 : _msg_metadata_parameters.model) || 'flux-schnell',\n                                                            size: ((_msg_metadata_parameters1 = msg.metadata.parameters) === null || _msg_metadata_parameters1 === void 0 ? void 0 : _msg_metadata_parameters1.size) || 'landscape',\n                                                            num_images: 1,\n                                                            seed\n                                                        }, msg.metadata.prompt_used);\n                                                    }\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 21\n                                        }, this),\n                                        msg.editResult && msg.editResult.editedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageEditResult__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                originalImage: msg.editResult.originalImage,\n                                                editedImage: msg.editResult.editedImage,\n                                                metadata: msg.editResult.metadata,\n                                                onReEdit: (newInstruction)=>{\n                                                    handleReEdit(msg.editResult, newInstruction);\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 297,\n                                    columnNumber: 17\n                                }, this)\n                            }, msg.id, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 296,\n                                columnNumber: 15\n                            }, this);\n                        }),\n                        isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white border border-gray-200 rounded-lg px-4 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500\",\n                                            children: \"AI正在思考...\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 362,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-t border-gray-200 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto space-y-3\",\n                    children: [\n                        showImageUpload && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium text-gray-900\",\n                                            children: \"上传图片进行编辑\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowImageUpload(false),\n                                            className: \"text-gray-500 hover:text-gray-700\",\n                                            children: \"✕\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageUpload__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    onImageUpload: handleImageUpload,\n                                    onImageEdit: handleImageEdit,\n                                    maxFiles: 3,\n                                    maxSize: 10\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 13\n                        }, this),\n                        optimizedPrompt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Chat_ImageGenerationPanel__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                onGenerate: handleImageGeneration,\n                                isGenerating: isGeneratingImage,\n                                optimizedPrompt: optimizedPrompt\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 400,\n                            columnNumber: 13\n                        }, this),\n                        uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border border-gray-200 rounded-lg p-3 bg-blue-50\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-700\",\n                                            children: [\n                                                \"准备发送的图片 (\",\n                                                uploadedImages.length,\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 413,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setUploadedImages([]),\n                                            className: \"text-gray-500 hover:text-gray-700 text-sm\",\n                                            children: \"清空\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: uploadedImages.map((img, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: img.url,\n                                                    alt: \"预览 \".concat(idx + 1),\n                                                    className: \"w-full h-20 object-cover rounded border\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setUploadedImages((prev)=>prev.filter((_, i)=>i !== idx));\n                                                    },\n                                                    className: \"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full text-xs flex items-center justify-center hover:bg-red-600\",\n                                                    children: \"\\xd7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, idx, true, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowImageUpload(!showImageUpload),\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center space-x-2\",\n                                    title: \"上传图片进行编辑\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"hidden sm:inline\",\n                                            children: \"图片\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: message,\n                                    onChange: (e)=>setMessage(e.target.value),\n                                    onKeyPress: (e)=>e.key === 'Enter' && sendMessage(),\n                                    placeholder: \"描述您想要生成的图像，或上传图片进行编辑...\",\n                                    disabled: isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: sendMessage,\n                                    disabled: !message.trim() && uploadedImages.length === 0 || isLoading || isGeneratingImage || isEditingImage,\n                                    className: \"px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors\",\n                                    children: isLoading ? '发送中...' : '发送'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图像生成示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        [\n                                            '可爱的小猫',\n                                            '未来城市',\n                                            '油画风景',\n                                            '卡通人物',\n                                            '抽象艺术'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setMessage(prompt),\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-full hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this),\n                                uploadedImages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 justify-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-gray-500 w-full text-center\",\n                                            children: \"图片编辑示例:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this),\n                                        [\n                                            '改为黑白风格',\n                                            '添加蓝天背景',\n                                            '移除背景',\n                                            '调整为夜晚场景',\n                                            '添加彩色滤镜'\n                                        ].map((prompt)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    if (uploadedImages.length > 0) {\n                                                        handleImageEdit(uploadedImages[0], prompt);\n                                                    }\n                                                },\n                                                disabled: isLoading || isGeneratingImage || isEditingImage,\n                                                className: \"px-3 py-1 text-sm bg-green-50 text-green-600 rounded-full hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\",\n                                                children: prompt\n                                            }, prompt, false, {\n                                                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 19\n                                            }, this))\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                    lineNumber: 376,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n                lineNumber: 375,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/cursor/进阶/Flux对话式应用/flux-chat-app/src/app/chat/page.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPage, \"khvJihUCBOhJ5VNz7pkb/Xea7Jk=\");\n_c = ChatPage;\nvar _c;\n$RefreshReg$(_c, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});